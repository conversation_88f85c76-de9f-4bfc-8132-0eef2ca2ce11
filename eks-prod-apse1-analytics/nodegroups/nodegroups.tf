data "aws_subnet" "this" {
  for_each = toset(var.private_subnets)
  id       = each.key
}

locals {
  availability_zone_subnets = {
    for s in data.aws_subnet.this : s.availability_zone => s.id...
  }
}

data "vault_kv_secret" "spotinst_creds" {
  path = "infra/spotinst"
}

module "default_ng" {
  source                   = "git::https://github.com/grofers/terraform-registry//modules/blinkit-stack/ocean?ref=nodegroup/v1.0.0"
  kind                     = "vng"
  create_fallback_asg      = true
  subnets_ids              = var.private_subnets
  cluster_name             = var.cluster_name
  cluster_version          = var.cluster_version
  ocean_id                 = var.ocean_id
  cluster_env              = var.cluster_env
  key_name                 = var.key_name
  nodegroup_name           = "default"
  ami_id                   = var.default_ami_id
  worker_security_group    = var.worker_security_group
  shared_security_group    = var.shared_security_group
  iam_instance_profile     = var.iam_instance_profile
  min_size                 = 0
  max_size                 = 10
  instance_types           = ["c5n.4xlarge", "c5.9xlarge", "c5a.4xlarge", "c5a.8xlarge", "c5n.9xlarge", "c6a.4xlarge", "c6a.8xlarge", "c6i.4xlarge", "c6i.8xlarge", "c6in.8xlarge", "c6in.4xlarge", "c7i.4xlarge", "c7i.8xlarge", "m5.8xlarge", "m5.4xlarge", "m5n.4xlarge", "m5n.8xlarge", "m6i.8xlarge", "m6i.4xlarge", "m6in.8xlarge", "m6in.4xlarge", "m7i.4xlarge", "m7i.8xlarge", "r5.4xlarge", "r5n.4xlarge", "r6i.4xlarge", "r6i.8xlarge", "r6in.4xlarge", "r7i.4xlarge", "r7i.8xlarge"]
  spot_percentage          = 100
  auto_headroom_percentage = 0
  # headroom = {
  #   cpu_per_unit    = 3072
  #   gpu_per_unit    = 0
  #   memory_per_unit = 2048
  #   num_of_units    = 5
  # }
}

module "monitoring_arm64_ng" {
  source                = "git::https://github.com/grofers/terraform-registry//modules/blinkit-stack/ocean?ref=nodegroup/v1.0.0"
  create_fallback_asg   = true
  kind                  = "vng"
  subnets_ids           = var.private_subnets
  cluster_name          = var.cluster_name
  cluster_version       = var.cluster_version
  ocean_id              = var.ocean_id
  cluster_env           = var.cluster_env
  key_name              = var.key_name
  nodegroup_name        = "monitoring_arm64"
  ami_id                = var.default_ami_id_arm64
  worker_security_group = var.worker_security_group
  shared_security_group = var.shared_security_group
  iam_instance_profile  = var.iam_instance_profile
  min_size              = 0
  max_size              = 25
  restrict_scale_down   = true
  instance_types = [
    "c6g.16xlarge", "c6g.2xlarge", "c6g.4xlarge", "c6g.12xlarge", "c6g.8xlarge", "c6gn.16xlarge", "c6gn.4xlarge", "c6gn.2xlarge", "c6gn.12xlarge", "c6gn.8xlarge", "c7g.12xlarge", "c7g.8xlarge", "c7g.2xlarge", "c7g.16xlarge", "c7g.4xlarge", "m6g.12xlarge", "m6g.4xlarge", "m6g.2xlarge", "m6g.16xlarge", "m6g.8xlarge", "m7g.8xlarge", "m7g.4xlarge", "m7g.16xlarge", "m7g.12xlarge", "m7g.2xlarge", "r6g.4xlarge", "r6g.16xlarge", "r6g.12xlarge", "r6g.8xlarge", "r6g.2xlarge", "r7g.2xlarge", "r7g.12xlarge", "r7g.8xlarge", "r7g.16xlarge", "r7g.4xlarge"
  ]
  spot_percentage          = 0
  auto_headroom_percentage = 0
  #   headroom = {
  #     cpuPerUnit    = 0
  #     memoryPerUnit = 0
  #     gpuPerUnit    = 0
  #     numOfUnits    = 0
  #   }
  additional_kubernetes_labels = {
    "workload" = "monitoring"
  }
  kubernetes_node_taints = [
    {
      key    = "workload"
      value  = "monitoring"
      effect = "NoSchedule"
    }
  ]
}