variable "cluster_name" {
  type = string
}

variable "cluster_env" {
  type    = string
  default = "prod"
  validation {
    condition     = contains(["prod", "stage"], var.cluster_env)
    error_message = "Cluster Env can be one of the following: prod or stage."
  }
}

variable "cluster_version" {
  type = string
}

variable "ocean_id" {
  type    = string
  default = null
}

variable "default_ami_id" {
  type = string
}

variable "default_ami_id_arm64" {
  type    = string
  default = null
}

variable "worker_security_group" {
  type = string
}

variable "shared_security_group" {
  type = string
}

variable "iam_instance_profile" {
  type = string
}

variable "private_subnets" {
  type = list(string)
}

variable "key_name" {
  type = string
}