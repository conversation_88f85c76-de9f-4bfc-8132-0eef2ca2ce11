cluster_name          = "eks-prod-apse1-analytics"
cluster_env           = "prod"
cluster_version       = "1.31"
key_name              = "blinkit-analytics-eks"
ocean_id              = "o-8835ddd2"
private_subnets       = ["subnet-03ca306593d8ce024", "subnet-06b312b17ae4764a3", "subnet-02b97d432cfb361a2", "subnet-09a51196787a38025", "subnet-091d72e204a90b03e", "subnet-0b813d0d0e3356875"]
default_ami_id        = "ami-0e9a0dcd86bf5016c"
default_ami_id_arm64  = "ami-0a16f9a3eb50d369e"
worker_security_group = "sg-081bdd300d6f2bcbe"
shared_security_group = "sg-0c9dd87442f725ef7"
iam_instance_profile  = "arn:aws:iam::183295456051:instance-profile/eks-prod-apse1-analytics-EKS-Worker-Role"