terraform {
  required_version = "~> 1.1.3"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.38.0"
    }
  }
  backend "s3" {
    bucket  = "blinkit-analytics-terraform"
    key     = "banalytics-infrastructure/blinkit-stack/eks-prod-apse1-analytics/targetgroups.tfstate"
    region  = "ap-southeast-1"
    profile = "blinkit_analytics"
  }
}

provider "aws" {
  region  = "ap-southeast-1"
  profile = "blinkit_analytics"
}