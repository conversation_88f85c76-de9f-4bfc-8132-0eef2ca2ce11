resource "random_string" "random" {
  length  = 4
  special = false
}
module "this" {
  for_each         = var.targetgroups
  source           = "git::https://github.com/grofers/terraform-registry//modules/blinkit-stack/targetgroup?ref=targetgroups/v1.0.0"
  cluster_name     = var.cluster_name
  vpc_id           = var.vpc_id
  names            = each.value.names
  service_name     = each.key
  random_prefix    = random_string.random.result
  port             = each.value.port
  health_check     = each.value.health_check
  slow_start       = try(each.value.slow_start, 0)
  protocol         = try(each.value.protocol, "HTTP")
  protocol_version = try(each.value.protocol_version, "HTTP1")
  additional_tags  = try(each.value.additional_tags, {})
}