terraform {
  required_version = "~> v1.3.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.38.0"
    }
    vault = {
      source  = "hashicorp/vault"
      version = "3.25.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "2.26.0"
    }
    kubectl = {
      source  = "alekc/kubectl"
      version = "2.0.4"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "2.12.1"
    }
  }
  backend "s3" {
    bucket  = "blinkit-analytics-terraform"
    key     = "banalytics-infrastructure/blinkit-stack/eks-prod-apse1-analytics/k8s-bootstrap.tfstate"
    region  = "ap-southeast-1"
    profile = "blinkit_analytics"
  }
}

provider "aws" {
  region  = "ap-southeast-1"
  profile = "blinkit_analytics"
}

provider "kubernetes" {
  host                   = data.aws_eks_cluster.this.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.this.certificate_authority[0].data)
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    args        = ["eks", "get-token", "--cluster-name", var.cluster_name]
    command     = "aws"
    env = {
      AWS_PROFILE = "blinkit_analytics"
      AWS_REGION  = "ap-southeast-1"
    }
  }
}

provider "helm" {
  kubernetes {
    host                   = data.aws_eks_cluster.this.endpoint
    cluster_ca_certificate = base64decode(data.aws_eks_cluster.this.certificate_authority[0].data)
    exec {
      api_version = "client.authentication.k8s.io/v1beta1"
      args        = ["eks", "get-token", "--cluster-name", var.cluster_name]
      command     = "aws"
      env = {
        AWS_PROFILE = "blinkit_analytics"
        AWS_REGION  = "ap-southeast-1"
      }
    }
  }
}

provider "kubectl" {
  host                   = data.aws_eks_cluster.this.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.this.certificate_authority[0].data)
  load_config_file       = false
  exec {
    api_version = "client.authentication.k8s.io/v1beta1"
    args        = ["eks", "get-token", "--cluster-name", var.cluster_name]
    command     = "aws"
    env = {
      AWS_PROFILE = "blinkit_analytics"
      AWS_REGION  = "ap-southeast-1"
    }
  }
}

provider "vault" {
  address          = "https://vault-${var.cluster_env}.grofer.io"
  skip_child_token = true
  auth_login_aws {
    role        = "atlantis"
    mount       = "aws-analytics"
    aws_profile = "blinkit_analytics"
  }
}
