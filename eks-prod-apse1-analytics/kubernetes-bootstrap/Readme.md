## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | ~> v1.3.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | 5.38.0 |
| <a name="requirement_helm"></a> [helm](#requirement\_helm) | 2.12.1 |
| <a name="requirement_kubectl"></a> [kubectl](#requirement\_kubectl) | 2.0.4 |
| <a name="requirement_kubernetes"></a> [kubernetes](#requirement\_kubernetes) | 2.26.0 |
| <a name="requirement_vault"></a> [vault](#requirement\_vault) | 3.25.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 5.38.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_kubernetes-bootstrap"></a> [kubernetes-bootstrap](#module\_kubernetes-bootstrap) | **************:grofers/terraform-registry//modules/blinkit-stack/kubernetes-bootstrap | dr-module |

## Resources

| Name | Type |
|------|------|
| [aws_eks_cluster.this](https://registry.terraform.io/providers/hashicorp/aws/5.38.0/docs/data-sources/eks_cluster) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_argocd_host"></a> [argocd\_host](#input\_argocd\_host) | n/a | `string` | n/a | yes |
| <a name="input_argocd_redis_db"></a> [argocd\_redis\_db](#input\_argocd\_redis\_db) | n/a | `number` | `0` | no |
| <a name="input_cluster_env"></a> [cluster\_env](#input\_cluster\_env) | n/a | `string` | n/a | yes |
| <a name="input_cluster_name"></a> [cluster\_name](#input\_cluster\_name) | n/a | `string` | n/a | yes |

## Outputs

No outputs.
