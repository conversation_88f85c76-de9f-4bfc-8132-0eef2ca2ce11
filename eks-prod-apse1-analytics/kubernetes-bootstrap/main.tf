data "aws_eks_cluster" "this" {
  name = var.cluster_name
}


module "kubernetes-bootstrap" {
  source                      = "git::https://github.com/grofers/terraform-registry//modules/blinkit-stack/kubernetes-bootstrap?ref=k8s-bootstrap/v1.0.0"
  cluster_service_account_env = var.cluster_env
  argocd_redis_db             = var.argocd_redis_db
  argocd_host                 = var.argocd_host
  argocd_redis_host           = var.argocd_redis_host
  eks = {
    cluster_name    = var.cluster_name
    kubernetes_host = data.aws_eks_cluster.this.endpoint
    kubernetes_ca   = base64decode(data.aws_eks_cluster.this.certificate_authority[0].data)
  }
}
