data "vault_kv_secret" "spotinst_creds" {
  path = "infra/spotinst"
}

data "aws_eks_cluster" "this" {
  name = var.cluster_name
}

# Deploy Spotinst-controller on cluster
resource "helm_release" "ocean-kubernetes-controller" {
  lifecycle {
    ignore_changes = [
      values, repository
    ]
  }
  chart      = "ocean-kubernetes-controller"
  version    = "0.1.57"
  repository = "https://charts.spot.io"
  wait       = true

  name             = "ocean-controller"
  namespace        = "kube-system"
  create_namespace = false
  values = [templatefile("./values.yaml",
    {
      ACCOUNT            = data.vault_kv_secret.spotinst_creds.data.spotinst_account_analytics
      TOKEN              = data.vault_kv_secret.spotinst_creds.data.spotinst_token
      CLUSTER_IDENTIFIER = var.cluster_name
    }
  )]

}
