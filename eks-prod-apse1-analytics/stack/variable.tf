variable "default_ami_id" {
  type = string
}

variable "cluster_name" {
  type = string
}

variable "cluster_version" {
  type = string
}

variable "cluster_service_ip_cidr" {
  type = string
}

variable "cluster_env" {
  type = string
}

variable "key_name" {
  type    = string
  default = "infra.jenkins"
}

variable "vpc_config" {
  type = object({
    id              = string
    public_subnets  = list(string)
    private_subnets = list(string)
    region          = string
    cidr_ranges     = list(string)
  })
}

variable "node_additional_security_groups" {
  type    = list(string)
  default = null
}

variable "ocean_config" {
  type = object({
    whitelist_ec2_types = list(string)
    root_volume_size    = optional(number, 120)
    autoscaler = object({
      autoscale_is_enabled                 = optional(bool, true)
      autoscale_is_auto_config             = optional(bool, true)
      autoscale_cooldown                   = optional(number, null)
      auto_headroom_percentage             = optional(number, 5)
      enable_automatic_and_manual_headroom = optional(bool, null)
      max_scale_down_percentage            = optional(number, 10)
      resource_limits = object({
        max_vcpu       = optional(number, 20000)
        max_memory_gib = optional(number, 100000)
      })
    })
  })
}

variable "infra_nodegroup" {
  type = object({
    instance_types   = optional(list(string), ["r5.2xlarge"])
    min_size         = optional(number, 3)
    max_size         = optional(number, 6)
    root_volume_size = optional(number, 120)
  })
}
