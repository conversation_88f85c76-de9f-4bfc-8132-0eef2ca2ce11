data "vault_kv_secret" "spotinst_creds" {
  path = "infra/spotinst"
}

data "aws_caller_identity" "current" {}

module "this" {
  source = "git::https://github.com/grofers/terraform-registry//modules/blinkit-stack/eks?ref=eks-stack/v1.0.0"

  cluster_name   = var.cluster_name
  cluster_env    = var.cluster_env
  default_ami_id = var.default_ami_id

  ocean_config = {
    spotinst_token             = data.vault_kv_secret.spotinst_creds.data.spotinst_token
    spotinst_account           = data.vault_kv_secret.spotinst_creds.data.spotinst_account
    additional_security_groups = var.node_additional_security_groups
    autoscaler                 = var.ocean_config.autoscaler
    whitelist_ec2_types        = var.ocean_config.whitelist_ec2_types
    root_volume_size           = var.ocean_config.root_volume_size
    key_name                   = var.key_name
  }

  infra_nodegroup = {
    instance_types             = var.infra_nodegroup.instance_types
    min_size                   = var.infra_nodegroup.min_size
    max_size                   = var.infra_nodegroup.max_size
    additional_security_groups = var.node_additional_security_groups
    root_volume_size           = var.infra_nodegroup.root_volume_size
    key_name                   = var.key_name
  }

  eks_configuration = {
    version      = var.cluster_version
    service_cidr = var.cluster_service_ip_cidr
  }

  vpc_config = var.vpc_config
}

output "demo" {
  value = data.aws_caller_identity.current.account_id
}