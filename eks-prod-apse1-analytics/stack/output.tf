output "cluster_name" {
  value       = module.this.cluster_name
  description = "EKS Controlplane name"
}

output "eks_worker_role" {
  value       = module.this.eks_worker_role
  description = "AWS IAM role arn for EKS nodegroup"
}

output "eks_infra_driver_role" {
  value       = module.this.eks_infra_driver_role
  description = "AWS IAM role arn for infra services"
}

output "eks_worker_role_profile" {
  value       = module.this.eks_worker_role_profile
  description = "Profile arn of EKS Worker IAM role"
}

output "node_security_group" {
  value       = module.this.node_security_group
  description = "Security group for nodegroups"
}

output "shared_security_group" {
  value       = module.this.shared_security_group
  description = "Shared Security group. To be attached with nodegroups."
}

output "ocean_id" {
  value       = module.this.ocean_id
  description = "Ocean-id of cluster on Spotinst"
}