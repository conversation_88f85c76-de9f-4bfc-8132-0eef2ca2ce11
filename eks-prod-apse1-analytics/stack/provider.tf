terraform {
  required_version = "~> v1.3.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.38.0"
    }
    vault = {
      source  = "hashicorp/vault"
      version = "3.25.0"
    }
    spotinst = {
      source  = "spotinst/spotinst"
      version = "1.161.0"
    }
  }
  backend "s3" {
    bucket  = "blinkit-analytics-terraform"
    key     = "banalytics-infrastructure/blinkit-stack/eks-prod-apse1-analytics/stack.tfstate"
    region  = "ap-southeast-1"
    profile = "blinkit_analytics"
  }
}

provider "aws" {
  region  = "ap-southeast-1"
  profile = "blinkit_analytics"
}

provider "vault" {
  address          = "https://vault-${var.cluster_env}.grofer.io"
  skip_child_token = true
  auth_login_aws {
    role        = "atlantis"
    mount       = "aws-analytics"
    aws_profile = "blinkit_analytics"
  }
}

provider "spotinst" {
  token   = data.vault_kv_secret.spotinst_creds.data.spotinst_token
  account = data.vault_kv_secret.spotinst_creds.data.spotinst_account_analytics
}