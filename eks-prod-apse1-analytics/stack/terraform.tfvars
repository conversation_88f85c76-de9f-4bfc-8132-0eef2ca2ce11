cluster_name            = "eks-prod-apse1-analytics"
cluster_env             = "prod"
cluster_version         = "1.31"
cluster_service_ip_cidr = "**********/16"
default_ami_id          = "ami-0e9a0dcd86bf5016c"
key_name                = "blinkit-analytics-eks"
vpc_config = {
  id              = "vpc-0e96120703d90e9e9"
  public_subnets  = ["subnet-09112db711646497b", "subnet-0f36a7948ad8458a2", "subnet-06a6f08bcc630e640"]
  private_subnets = ["subnet-03ca306593d8ce024", "subnet-06b312b17ae4764a3", "subnet-02b97d432cfb361a2", "subnet-09a51196787a38025", "subnet-091d72e204a90b03e", "subnet-0b813d0d0e3356875"]
  region          = "ap-southeast-1"
  cidr_ranges     = ["*********/16", "**********/16", "**********/16"]
}

# node_additional_security_groups = [ "sg-bf73b0da" ]

ocean_config = {
  autoscaler = {
    autoscale_is_enabled                 = true
    autoscale_is_auto_config             = true
    max_scale_down_percentage            = 2
    auto_headroom_percentage             = 0
    enable_automatic_and_manual_headroom = true
    resource_limits = {
      max_vcpu : 20000
      max_memory_gib : 100000
    }
  }
  whitelist_ec2_types = ["c6gn.xlarge", "c6gn.large", "c5.9xlarge", "c5.4xlarge", "c5.2xlarge", "c5a.4xlarge", "c5a.2xlarge", "c5a.8xlarge", "c5ad.4xlarge", "c5ad.2xlarge", "c5ad.8xlarge", "c5d.4xlarge", "c5d.9xlarge", "c5n.4xlarge", "c5n.2xlarge", "c5n.9xlarge", "c6a.4xlarge", "c6a.2xlarge", "c6a.8xlarge", "c6i.4xlarge", "c6i.8xlarge", "c6i.2xlarge", "c6id.4xlarge", "c6id.8xlarge", "c6id.2xlarge", "c6in.8xlarge", "c6in.2xlarge", "c6in.4xlarge", "c7g.8xlarge", "c7g.2xlarge", "c7i.4xlarge", "c7i.2xlarge", "c7i.8xlarge", "d2.4xlarge", "d3.4xlarge", "g2.8xlarge", "g3.4xlarge", "g4dn.4xlarge", "g4dn.8xlarge", "hs1.8xlarge", "i2.4xlarge", "i3.4xlarge", "inf1.6xlarge", "m5.8xlarge", "m5.2xlarge", "m5.4xlarge", "m5a.4xlarge", "m5d.8xlarge", "m5d.4xlarge", "m5dn.8xlarge", "m5dn.4xlarge", "m5n.4xlarge", "m5n.8xlarge", "m5zn.6xlarge", "m6i.8xlarge", "m6i.4xlarge", "m7g.4xlarge", "m7g.2xlarge", "m7i.4xlarge", "m7i.8xlarge", "m7i.2xlarge", "r4.4xlarge", "r5.8xlarge", "r5.4xlarge", "r5b.4xlarge", "r5d.4xlarge", "r5dn.4xlarge", "r5n.4xlarge", "r5n.2xlarge", "r6i.4xlarge", "r6i.12xlarge", "r6i.16xlarge", "r6i.8xlarge", "r7g.2xlarge", "r7g.xlarge", "r7i.2xlarge", "r7i.4xlarge", "r7i.8xlarge", "r6in.2xlarge", "m6in.8xlarge", "m6in.4xlarge", "r6in.4xlarge"]
}

infra_nodegroup = {
  instance_types = ["c5.2xlarge", "c5.4xlarge", "c5.2xlarge", "c5n.4xlarge", "c5n.2xlarge"]
  min_size       = 4
  max_size       = 15
}