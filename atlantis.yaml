version: 3
projects:
# Prod EKS Stack
  - name: eks-stack-nodegroup
    dir: "eks-prod-apse1-analytics/nodegroups"
    terraform_version: v1.1.3
  - name: eks-stack-targetgroups
    dir: "eks-prod-apse1-analytics/targetgroups"
    terraform_version: v1.1.3
  - name: eks-stack-vault
    dir: "eks-prod-apse1-analytics/vault"
    terraform_version: v1.1.3
  - name: eks-stack
    dir: "eks-prod-apse1-analytics/stack"
    terraform_version: v1.3.0
  - name: eks-stack-k8s-bootstrap
    dir: "eks-prod-apse1-analytics/kubernetes-bootstrap"
    terraform_version: v1.3.0

# Preprod EKS Stack
  - name: eks-preprod-stack-nodegroup
    dir: "eks-preprod-apse1-analytics/nodegroups"
    terraform_version: v1.1.3
  - name: eks-preprod-stack-targetgroups
    dir: "eks-preprod-apse1-analytics/targetgroups"
    terraform_version: v1.1.3
  - name: eks-preprod-stack-vault
    dir: "eks-preprod-apse1-analytics/vault"
    terraform_version: v1.1.3
  - name: eks-preprod-stack
    dir: "eks-preprod-apse1-analytics/stack"
    terraform_version: v1.3.0
  - name: eks-preprod-stack-k8s-bootstrap
    dir: "eks-preprod-apse1-analytics/kubernetes-bootstrap"
    terraform_version: v1.3.0

  - name: trino_preprod
    dir: "vpc/services/trino/preprod"
    terraform_version: v1.1.7
    autoplan:
      when_modified: [
        "vpc/services/trino/preprod/*.tf",
        "vpc/services/trino/preprod/*.tfvars"
      ]
      enabled: false

  - name: trino_prod
    dir: "vpc/services/trino/prod"
    terraform_version: v1.1.7
    autoplan:
      when_modified: [
          "vpc/services/trino/prod/*.tf",
          "vpc/services/trino/prod/*.tfvars"
      ]
      enabled: false

  - name: cdp_preprod
    dir: "vpc/services/cdp/preprod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
          "vpc/services/cdp/preprod/*.tf",
          "vpc/services/cdp/preprod/*.tfvars"
      ]
      enabled: false
  - name: cdp_prod
    dir: "vpc/services/cdp/prod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
          "vpc/services/cdp/prod/*.tf",
          "vpc/services/cdp/prod/*.tfvars"
      ]
      enabled: false

  - name: cdp_starrocks_prod_iam
    dir: "services-aws-resources/cdp-starrocks/iam/cdp-starrocks/prod"
    terraform_version: v1.1.7
    autoplan:
      when_modified: [
        "services-aws-resources/cdp-starrocks/iam/cdp-starrocks/prod/*.tf",
        "services-aws-resources/cdp-starrocks/iam/cdp-starrocks/prod/*.tfvars"
      ]
      enabled: true

  - name: cdp_emr_preprod_iam
    dir: "services-aws-resources/cdp-emr/iam/cdp-emr/preprod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/cdp-emr/iam/cdp-emr/preprod/*.tf",
        "services-aws-resources/cdp-emr/iam/cdp-emr/preprod/*.tfvars"
      ]
      enabled: true

  - name: cdp_emr_prod_iam
    dir: "services-aws-resources/cdp-emr/iam/cdp-emr/prod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/cdp-emr/iam/cdp-emr/prod/*.tf",
        "services-aws-resources/cdp-emr/iam/cdp-emr/prod/*.tfvars"
      ]
      enabled: true

  - name: cdp_service_preprod_iam
    dir: "services-aws-resources/cdp-service/iam/cdp-service/preprod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/cdp-service/iam/cdp-service/preprod/*.tf",
        "services-aws-resources/cdp-service/iam/cdp-service/preprod/*.tfvars"
      ]
      enabled: true

  - name: cdp_service_prod_iam
    dir: "services-aws-resources/cdp-service/iam/cdp-service/prod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/cdp-service/iam/cdp-service/prod/*.tf",
        "services-aws-resources/cdp-service/iam/cdp-service/prod/*.tfvars"
      ]
      enabled: true

  - name: lambda_service_preprod_iam
    dir: "services-aws-resources/lambda-service/iam/lambda-service/preprod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/lambda-service/iam/lambda-service/preprod/*.tf",
        "services-aws-resources/lambda-service/iam/lambda-service/preprod/*.tfvars"
      ]
      enabled: true

  - name: lambda_service_prod_iam
    dir: "services-aws-resources/lambda-service/iam/lambda-service/prod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/lambda-service/iam/lambda-service/prod/*.tf",
        "services-aws-resources/lambda-service/iam/lambda-service/prod/*.tfvars"
      ]
      enabled: true

  - name: bedrock_service_preprod_iam
    dir: "services-aws-resources/bedrock-service/iam/bedrock-service/preprod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/bedrock-service/iam/bedrock-service/preprod/*.tf",
        "services-aws-resources/bedrock-service/iam/bedrock-service/preprod/*.tfvars"
      ]
      enabled: true

  - name: bedrock_service_prod_iam
    dir: "services-aws-resources/bedrock-service/iam/bedrock-service/prod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/bedrock-service/iam/bedrock-service/prod/*.tf",
        "services-aws-resources/bedrock-service/iam/bedrock-service/prod/*.tfvars"
      ]
      enabled: true

  - name: hive_meta_store_preprod_iam
    dir: "services-aws-resources/hive-meta-store/iam/hive-meta-store/preprod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/hive-meta-store/iam/hive-meta-store/preprod/*.tf",
        "services-aws-resources/hive-meta-store/iam/hive-meta-store/preprod/*.tfvars"
      ]
      enabled: true

  - name: pinot_preprod_primary_iam
    dir: "services-aws-resources/pinot-preprod/iam/pinot-preprod/primary"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/pinot-preprod/iam/pinot-preprod/primary/*.tf",
        "services-aws-resources/pinot-preprod/iam/pinot-preprod/primary/*.tfvars"
      ]
      enabled: true

  - name: warpstream_blinkit_analytics_bridge_kafka_analytics_preprod_iam
    dir: "services-aws-resources/warpstream/iam/warpstream-blinkit-analytics-bridge-kafka-analytics/preprod/"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/warpstream/iam/warpstream-blinkit-analytics-bridge-kafka-analytics/preprod/*.tf",
        "services-aws-resources/warpstream/iam/warpstream-blinkit-analytics-bridge-kafka-analytics/preprod/*.tfvars"
      ]
      enabled: true

  - name: warpstream_blinkit_analytics_cep_data_platform_preprod_iam
    dir: "services-aws-resources/warpstream/iam/warpstream-blinkit-analytics-cep-data-platform/preprod/"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "services-aws-resources/warpstream/iam/warpstream-blinkit-analytics-cep-data-platform/preprod/*.tf",
        "services-aws-resources/warpstream/iam/warpstream-blinkit-analytics-cep-data-platform/preprod/*.tfvars"
      ]
      enabled: true

  - name: preprod_trino
    dir: "vpc/preprod/services/trino"
    terraform_version: v1.1.7
    autoplan:
      when_modified: [
        "vpc/preprod/services/trino/*.tf",
        "vpc/preprod/services/trino/*.tfvars"
      ]
      enabled: false

  - name: planner_starrocks_preprod
    dir: "vpc/services/planner_starrocks/preprod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/services/planner_starrocks/preprod/*.tf",
        "vpc/services/planner_starrocks/preprod/*.tfvars"
      ]
      enabled: true

  - name: planner_starrocks_prod
    dir: "vpc/services/planner_starrocks/prod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/services/planner_starrocks/prod/*.tf",
        "vpc/services/planner_starrocks/prod/*.tfvars"
      ]
      enabled: true

  - name: prod_services_cluster_v2
    dir: "vpc/services/prod_services_cluster_v2"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/services/prod_services_cluster_v2/*.tf",
        "vpc/services/prod_services_cluster_v2/*.tfvars"
      ]
      enabled: true

  - name: preprod_services_cluster_v2
    dir: "vpc/preprod/services/preprod_services_cluster_v2"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/preprod/services/preprod_services_cluster_v2/*.tf",
        "vpc/preprod/services/preprod_services_cluster_v2/*.tfvars"
      ]
      enabled: true

  - name: opa_redis_prod
    dir: "vpc/services/opa-redis/prod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/services/opa-redis/prod/*.tf",
        "vpc/services/opa-redis/prod/*.tfvars"
      ]
      enabled: true

  - name: opa_redis_preprod
    dir: "vpc/preprod/services/opa-redis"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/preprod/services/opa-redis/*.tf",
        "vpc/preprod/services/opa-redis/*.tfvars"
      ]
      enabled: true

  - name: preprod_mysql_db
    dir: "vpc/services/preprod-mysql-db/preprod"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/services/preprod-mysql-db/preprod/*.tf",
        "vpc/services/preprod-mysql-db/preprod/*.tfvars"
      ]
      enabled: true

  - name: preprod_trino_gateway
    dir: "vpc/preprod/services/trino_gateway"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/preprod/services/trino_gateway/*.tf",
        "vpc/preprod/services/trino_gateway/*.tfvars"
      ]
      enabled: true

  - name: prod_trino_gateway
    dir: "vpc/services/trino_gateway"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/services/trino_gateway/*.tf",
        "vpc/services/trino_gateway/*.tfvars"
      ]
      enabled: true

  - name: trino_ecs_etl_cluster_8
    dir: "vpc/services/trino-ecs/prod/etl/cluster-8"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/services/trino-ecs/prod/etl/cluster-8/*.tf",
        "vpc/services/trino-ecs/prod/etl/cluster-8/*.tfvars"
      ]
      enabled: true
  - name: trino_ecs_etl_cluster_9
    dir: "vpc/services/trino-ecs/prod/etl/cluster-9"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/services/trino-ecs/prod/etl/cluster-9/*.tf",
        "vpc/services/trino-ecs/prod/etl/cluster-9/*.tfvars"
      ]
      enabled: true
  - name: trino_ecs_etl_cluster_10
    dir: "vpc/services/trino-ecs/prod/etl/cluster-10"
    terraform_version: v1.4.1
    autoplan:
      when_modified: [
        "vpc/services/trino-ecs/prod/etl/cluster-10/*.tf",
        "vpc/services/trino-ecs/prod/etl/cluster-10/*.tfvars"
      ]
      enabled: true