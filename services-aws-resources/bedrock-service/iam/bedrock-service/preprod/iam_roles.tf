# IAM Roles for Bedrock Service preprod deployment
# Bedrock Service Role with specific permissions

# Trust policy for Bedrock Service role
resource "aws_iam_role" "bedrock_service_role" {
  name = "${var.environment}-${var.service_name}-service-role"
  path = "/services/${var.service_name}/"

  # Trust policy allowing Bedrock service to assume this role
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "bedrock.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
      {
        Sid = "AmazonBedrockKnowledgeBaseTrustPolicy"
        Effect = "Allow"
        Principal = {
          Service = "bedrock.amazonaws.com"
        }
        Action = "sts:AssumeRole"
        Condition = {
          StringEquals = {
            "aws:SourceAccount" = "************"
          }
          ArnLike = {
            "aws:SourceArn" = "arn:aws:bedrock:ap-southeast-1:************:knowledge-base/*"
          }
        }
      }
    ]
  })

  tags = {
    "cost:application"       = var.service_name
    "cost:component"         = "iam"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  }
}

# Bedrock model invocation and Lambda function access policy
resource "aws_iam_policy" "bedrock_service_policy" {
  name        = "${var.service_namespace}-${var.environment}-bedrock-service-policy"
  description = "Bedrock model invocation and Lambda function access policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "bedrock:InvokeModel",
          "bedrock:InvokeModelWithResponseStream",
          "bedrock:GetInferenceProfile",
          "bedrock:GetFoundationModel"
        ]
        Resource = [
          "arn:aws:bedrock:ap-southeast-1:************:inference-profile/*",
          "arn:aws:bedrock:*::foundation-model/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "bedrock:*"
        ]
        Resource = "arn:aws:bedrock:ap-southeast-1:************:knowledge-base/*"
      },
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = [
          "arn:aws:lambda:ap-southeast-1:************:function:test-bedrock",
          "arn:aws:lambda:ap-southeast-1:************:function:naturalLanguageToSql"
        ]
      }
    ]
  })
}

# S3 access policy for Bedrock service
resource "aws_iam_policy" "bedrock_s3_access_policy" {
  name        = "${var.service_namespace}-${var.environment}-bedrock-s3-access"
  description = "S3 access policy for Bedrock service"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket",
          "s3:DeleteObject",
          "s3:GetBucketLocation",
          "s3:GetObjectVersion",
          "s3:PutObjectAcl",
          "s3:GetBucketAcl",
          "s3:ListBucketMultipartUploads",
          "s3:ListMultipartUploadParts",
          "s3:AbortMultipartUpload"
        ]
        Resource = [
          "arn:aws:s3:::blinkit-analytics",
          "arn:aws:s3:::blinkit-analytics/bedrock/*"
        ]
      }
    ]
  })
}

# Policy attachment to the Bedrock service role
resource "aws_iam_role_policy_attachment" "bedrock_service_policy_attachment" {
  role       = aws_iam_role.bedrock_service_role.name
  policy_arn = aws_iam_policy.bedrock_service_policy.arn
}

# Attach S3 access policy to Bedrock service role
resource "aws_iam_role_policy_attachment" "bedrock_s3_access_policy_attachment" {
  role       = aws_iam_role.bedrock_service_role.name
  policy_arn = aws_iam_policy.bedrock_s3_access_policy.arn
}

# Attach AWS managed policies for Bedrock Knowledge Base
resource "aws_iam_role_policy_attachment" "bedrock_kb_s3_policy_attachment" {
  role       = aws_iam_role.bedrock_service_role.name
  policy_arn = "arn:aws:iam::************:policy/service-role/AmazonBedrockS3PolicyForKnowledgeBase_dnp9r"
}

resource "aws_iam_role_policy_attachment" "bedrock_kb_oss_policy_attachment" {
  role       = aws_iam_role.bedrock_service_role.name
  policy_arn = "arn:aws:iam::************:policy/service-role/AmazonBedrockOSSPolicyForKnowledgeBase_dnp9r"
}

resource "aws_iam_role_policy_attachment" "bedrock_kb_foundation_model_policy_attachment" {
  role       = aws_iam_role.bedrock_service_role.name
  policy_arn = "arn:aws:iam::************:policy/service-role/AmazonBedrockFoundationModelPolicyForKnowledgeBase_dnp9r"
}
