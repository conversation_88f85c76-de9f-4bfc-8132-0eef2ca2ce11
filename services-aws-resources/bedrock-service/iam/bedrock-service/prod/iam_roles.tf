# IAM Roles for Bedrock Service prod deployment
# Bedrock Service Role with specific permissions

# Trust policy for Bedrock Service role
resource "aws_iam_role" "bedrock_service_role" {
  name = "${var.environment}-${var.service_name}-service-role"
  path = "/services/${var.service_name}/"

  # Trust policy allowing Bedrock service to assume this role
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "bedrock.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = {
    "cost:application"       = var.service_name
    "cost:component"         = "iam"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  }
}

# Bedrock model invocation and Lambda function access policy
resource "aws_iam_policy" "bedrock_service_policy" {
  name        = "${var.service_namespace}-${var.environment}-bedrock-service-policy"
  description = "Bedrock model invocation and Lambda function access policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "bedrock:InvokeModel",
          "bedrock:InvokeModelWithResponseStream"
        ]
        Resource = "arn:aws:bedrock:*::foundation-model/*"
      },
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = [
          "arn:aws:lambda:ap-southeast-1:183295456051:function:test-bedrock"
        ]
      }
    ]
  })
}

# Policy attachment to the Bedrock service role
resource "aws_iam_role_policy_attachment" "bedrock_service_policy_attachment" {
  role       = aws_iam_role.bedrock_service_role.name
  policy_arn = aws_iam_policy.bedrock_service_policy.arn
}
