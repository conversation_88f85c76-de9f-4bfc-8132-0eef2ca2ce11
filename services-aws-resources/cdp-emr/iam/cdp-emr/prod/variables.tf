variable "environment" {
  description = "Environment name"
  type        = string
}

variable "service_name" {
  description = "Name of the service"
  type        = string
}

variable "aws_provider_region" {
  description = "AWS region for the provider"
  type        = string
}

variable "aws_profile" {
  description = "AWS profile to use"
  type        = string
}

variable "aws_account_id" {
  description = "AWS account ID"
  type        = string
}

variable "iam_prefix" {
  description = "Prefix for IAM resources"
  type        = string
}

variable "service_namespace" {
  description = "Kubernetes namespace for the service"
  type        = string
}