output "cdp_emr_role_arn" {
  description = "ARN of the CDP EMR IAM role"
  value       = aws_iam_role.cdp_emr_role.arn
}

output "cdp_emr_role_name" {
  description = "Name of the CDP EMR IAM role"
  value       = aws_iam_role.cdp_emr_role.name
}

output "cdp_emr_s3_policy_arn" {
  description = "ARN of the CDP EMR S3 policy"
  value       = aws_iam_policy.cdp_emr_s3_policy.arn
}

output "cdp_emr_instance_profile_arn" {
  description = "ARN of the CDP EMR instance profile"
  value       = aws_iam_instance_profile.cdp_emr_instance_profile.arn
}

output "cdp_emr_instance_profile_name" {
  description = "Name of the CDP EMR instance profile"
  value       = aws_iam_instance_profile.cdp_emr_instance_profile.name
}