# IAM Role for CDP EMR preprod deployment
resource "aws_iam_role" "cdp_emr_role" {
  name = "${var.environment}-blinkit-${var.iam_prefix}-role"
  path = "/"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "defaultec2"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
      {
        Sid    = "SelfAssume"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${var.aws_account_id}:role/${var.environment}-blinkit-${var.iam_prefix}-role"
        }
        Action = "sts:AssumeRole"
      },
      {
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::************:root"
        }
        Action = "sts:AssumeRole"
        Condition = {}
      }
    ]
  })
}

# S3 access policy for CDP EMR
resource "aws_iam_policy" "cdp_emr_s3_policy" {
  name = "${var.environment}-blinkit-${var.iam_prefix}-s3-access-policy"
  path = "/"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "VisualEditor0"
        Effect = "Allow"
        Action = [
          "s3:DeleteObjectVersion",
          "s3:GetObjectVersionTagging",
          "s3:PutObjectVersionTagging",
          "s3:GetObjectVersionTorrent",
          "s3:PutObject",
          "s3:GetObjectAcl",
          "s3:GetObject",
          "s3:GetObjectTorrent",
          "s3:AbortMultipartUpload",
          "s3:PutObjectVersionAcl",
          "s3:GetObjectVersionAcl",
          "s3:GetObjectTagging",
          "s3:PutObjectTagging",
          "s3:DeleteObject",
          "s3:PutObjectAcl",
          "s3:GetObjectVersion",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::blinkit-analytics",
          "arn:aws:s3:::blinkit-analytics/*"
        ]
      },
      {
        Sid    = "blinkitProd"
        Effect = "Allow"
        Action = [
          "s3:Get*",
          "s3:List*"
        ]
        Resource = [
          "arn:aws:s3:::prod-data-lake-hudi-applications",
          "arn:aws:s3:::prod-data-lake-hudi-applications/*",
          "arn:aws:s3:::prod-data-lake-hudi-segment-events",
          "arn:aws:s3:::prod-data-lake-hudi-segment-events/*",
          "arn:aws:s3:::prod-data-lake-hudi-rudder-events",
          "arn:aws:s3:::prod-data-lake-hudi-rudder-events/*",
          "arn:aws:s3:::prod-data-lake-hudi-branch-events",
          "arn:aws:s3:::prod-data-lake-hudi-branch-events/*",
          "arn:aws:s3:::prod-data-feature-store",
          "arn:aws:s3:::prod-data-feature-store/*",
          "arn:aws:s3:::prod-dse-rudder-datetime-chunks-sgp",
          "arn:aws:s3:::prod-dse-rudder-datetime-chunks-sgp/*",
          "arn:aws:s3:::blinkit-data-spark-production",
          "arn:aws:s3:::blinkit-data-spark-production/*",
          "arn:aws:s3:::prod-dse-backend-events-raw",
          "arn:aws:s3:::prod-dse-backend-events-raw/*"
        ]
      },
      {
        Sid    = "zanalytics"
        Effect = "Allow"
        Action = [
          "s3:List*",
          "s3:Get*"
        ]
        Resource = [
          "arn:aws:s3:::blinkit-derived-etls",
          "arn:aws:s3:::blinkit-derived-etls/*"
        ]
      },
      {
        Sid    = "banalytics"
        Effect = "Allow"
        Action = [
          "s3:List*",
          "s3:Get*"
        ]
        Resource = [
          "arn:aws:s3:::blinkit-analytics/config/*",
          "arn:aws:s3:::blinkit-analytics"
        ]
      },
      {
        Sid    = "testBuckets"
        Effect = "Allow"
        Action = ["s3:*"]
        Resource = [
          "arn:aws:s3:::grofers-test-dse-singapore",
          "arn:aws:s3:::grofers-test-dse",
          "arn:aws:s3:::aws-logs-183295456051-ap-southeast-1",
          "arn:aws:s3:::grofers-test-dse-singapore/*",
          "arn:aws:s3:::grofers-test-dse/*",
          "arn:aws:s3:::aws-logs-183295456051-ap-southeast-1/*"
        ]
      },
      {
        Sid    = "zanalyticsStaging"
        Effect = "Allow"
        Action = [
          "s3:DeleteObjectVersion",
          "s3:GetObjectVersionTagging",
          "s3:PutObjectVersionTagging",
          "s3:GetObjectVersionTorrent",
          "s3:PutObject",
          "s3:GetObjectAcl",
          "s3:GetObject",
          "s3:GetObjectTorrent",
          "s3:AbortMultipartUpload",
          "s3:PutObjectVersionAcl",
          "s3:GetObjectVersionAcl",
          "s3:GetObjectTagging",
          "s3:PutObjectTagging",
          "s3:DeleteObject",
          "s3:PutObjectAcl",
          "s3:GetObjectVersion",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::blinkit-data-staging",
          "arn:aws:s3:::blinkit-data-staging/*"
        ]
      }

    ]
  })
}

# Attach the S3 policy to the CDP EMR role
resource "aws_iam_role_policy_attachment" "cdp_emr_s3_policy_attachment" {
  role       = aws_iam_role.cdp_emr_role.name
  policy_arn = aws_iam_policy.cdp_emr_s3_policy.arn
}