# IAM Roles for Lambda Service prod deployment
# Lambda Execution Role with specific permissions

# Trust policy for Lambda Service role
resource "aws_iam_role" "lambda_execution_role" {
  name = "${var.environment}-${var.service_name}-execution-role"
  path = "/services/${var.service_name}/"

  # Trust policy allowing Lambda service to assume this role
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = {
    "cost:application"       = var.service_name
    "cost:component"         = "iam"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  }
}

# CloudWatch Logs Full Access Policy
resource "aws_iam_policy" "lambda_cloudwatch_logs_policy" {
  name        = "${var.service_namespace}-${var.environment}-cloudwatch-logs-policy"
  description = "CloudWatch Logs full access policy for Lambda functions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "CloudWatchLogsFullAccess"
        Effect = "Allow"
        Action = [
          "logs:*",
          "cloudwatch:GenerateQuery",
          "cloudwatch:GenerateQueryResultsSummary"
        ]
        Resource = "*"
      }
    ]
  })
}

# CloudWatch monitoring and observability policy
resource "aws_iam_policy" "lambda_cloudwatch_monitoring_policy" {
  name        = "${var.service_namespace}-${var.environment}-cloudwatch-monitoring-policy"
  description = "CloudWatch monitoring and observability policy for Lambda functions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "autoscaling:Describe*",
          "cloudwatch:*",
          "logs:*",
          "sns:*",
          "iam:GetPolicy",
          "iam:GetPolicyVersion",
          "iam:GetRole",
          "oam:ListSinks"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = "iam:CreateServiceLinkedRole"
        Resource = "arn:aws:iam::*:role/aws-service-role/events.amazonaws.com/AWSServiceRoleForCloudWatchEvents*"
        Condition = {
          StringLike = {
            "iam:AWSServiceName" = "events.amazonaws.com"
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "oam:ListAttachedLinks"
        ]
        Resource = "arn:aws:oam:*:*:sink/*"
      }
    ]
  })
}

# Policy attachments to the Lambda execution role
resource "aws_iam_role_policy_attachment" "lambda_cloudwatch_logs_attachment" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = aws_iam_policy.lambda_cloudwatch_logs_policy.arn
}

resource "aws_iam_role_policy_attachment" "lambda_cloudwatch_monitoring_attachment" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = aws_iam_policy.lambda_cloudwatch_monitoring_policy.arn
}
