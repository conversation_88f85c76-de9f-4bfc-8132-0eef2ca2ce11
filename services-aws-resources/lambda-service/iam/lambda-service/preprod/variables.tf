variable "service_name" {
  type        = string
  description = "Name of the service"
}

variable "environment" {
  type        = string
  default     = "preprod"
  description = "Environment name (prod, preprod, dev)"
}

variable "aws_provider_region" {
  type        = string
  description = "AWS region for the provider"
}

variable "aws_account_id" {
  type        = string
  description = "AWS Account ID"
}

variable "aws_profile" {
  type        = string
  description = "AWS profile to use for authentication"
}

variable "iam_prefix" {
  type        = string
  description = "Prefix for IAM resources"
}

variable "service_namespace" {
  type        = string
  description = "Namespace for cost allocation and resource naming"
}
