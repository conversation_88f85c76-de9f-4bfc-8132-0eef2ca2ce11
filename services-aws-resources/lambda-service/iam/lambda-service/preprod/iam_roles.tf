# IAM Roles for Lambda Service preprod deployment
# Lambda Execution Role with specific permissions

# Trust policy for Lambda Service role
resource "aws_iam_role" "lambda_execution_role" {
  name = "${var.environment}-${var.service_name}-execution-role"
  path = "/services/${var.service_name}/"

  # Trust policy allowing Lambda service to assume this role
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "bedrock.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
      {
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
      {
        Sid = "AmazonBedrockKnowledgeBaseTrustPolicy"
        Effect = "Allow"
        Principal = {
          Service = "bedrock.amazonaws.com"
        }
        Action = "sts:AssumeRole"
        Condition = {
          StringEquals = {
            "aws:SourceAccount" = "************"
          }
          ArnLike = {
            "aws:SourceArn" = "arn:aws:bedrock:ap-southeast-1:************:knowledge-base/*"
          }
        }
      }
    ]
  })

  tags = {
    "cost:application"       = var.service_name
    "cost:component"         = "iam"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  }
}

# CloudWatch Logs Full Access Policy
resource "aws_iam_policy" "lambda_cloudwatch_logs_policy" {
  name        = "${var.service_namespace}-${var.environment}-cloudwatch-logs-policy"
  description = "CloudWatch Logs full access policy for Lambda functions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "CloudWatchLogsFullAccess"
        Effect = "Allow"
        Action = [
          "logs:*",
          "cloudwatch:GenerateQuery",
          "cloudwatch:GenerateQueryResultsSummary"
        ]
        Resource = "*"
      }
    ]
  })
}

# CloudWatch monitoring and observability policy
resource "aws_iam_policy" "lambda_cloudwatch_monitoring_policy" {
  name        = "${var.service_namespace}-${var.environment}-cloudwatch-monitoring-policy"
  description = "CloudWatch monitoring and observability policy for Lambda functions"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "autoscaling:Describe*",
          "cloudwatch:*",
          "logs:*",
          "sns:*",
          "iam:GetPolicy",
          "iam:GetPolicyVersion",
          "iam:GetRole",
          "oam:ListSinks"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = "iam:CreateServiceLinkedRole"
        Resource = "arn:aws:iam::*:role/aws-service-role/events.amazonaws.com/AWSServiceRoleForCloudWatchEvents*"
        Condition = {
          StringLike = {
            "iam:AWSServiceName" = "events.amazonaws.com"
          }
        }
      },
      {
        Effect = "Allow"
        Action = [
          "oam:ListAttachedLinks"
        ]
        Resource = "arn:aws:oam:*:*:sink/*"
      }
    ]
  })
}

# Bedrock model invocation and Lambda function access policy
resource "aws_iam_policy" "lambda_service_policy" {
  name        = "${var.service_namespace}-${var.environment}-bedrock-service-policy"
  description = "Bedrock model invocation and Lambda function access policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "bedrock:InvokeModel",
          "bedrock:InvokeModelWithResponseStream",
          "bedrock:GetInferenceProfile",
          "bedrock:GetFoundationModel"
        ]
        Resource = [
          "arn:aws:bedrock:ap-southeast-1:************:inference-profile/*",
          "arn:aws:bedrock:*::foundation-model/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "bedrock:*"
        ]
        Resource = "arn:aws:bedrock:ap-southeast-1:************:knowledge-base/*"
      },
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = [
          "arn:aws:lambda:ap-southeast-1:************:function:test-bedrock",
          "arn:aws:lambda:ap-southeast-1:************:function:naturalLanguageToSql"
        ]
      }
    ]
  })
}


# Policy attachments to the Lambda execution role
resource "aws_iam_role_policy_attachment" "lambda_cloudwatch_logs_attachment" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = aws_iam_policy.lambda_cloudwatch_logs_policy.arn
}

resource "aws_iam_role_policy_attachment" "lambda_cloudwatch_monitoring_attachment" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = aws_iam_policy.lambda_cloudwatch_monitoring_policy.arn
}

# Policy attachment to the Bedrock service role
resource "aws_iam_role_policy_attachment" "lambda_service_policy_attachment" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = aws_iam_policy.lambda_service_policy.arn
}


# Attach AWS managed policies for Bedrock Knowledge Base
resource "aws_iam_role_policy_attachment" "bedrock_kb_s3_policy_attachment" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = "arn:aws:iam::************:policy/service-role/AmazonBedrockS3PolicyForKnowledgeBase_dnp9r"
}

resource "aws_iam_role_policy_attachment" "bedrock_kb_oss_policy_attachment" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = "arn:aws:iam::************:policy/service-role/AmazonBedrockOSSPolicyForKnowledgeBase_dnp9r"
}

resource "aws_iam_role_policy_attachment" "bedrock_kb_foundation_model_policy_attachment" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = "arn:aws:iam::************:policy/service-role/AmazonBedrockFoundationModelPolicyForKnowledgeBase_dnp9r"
}