# If planning to change the name then ensure it get updated on condition block too.
resource "aws_iam_role" "serviceaccount-role" {
  name               = "blinkit-${var.iam_prefix}-${var.environment}-eks-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "defaultec2"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
      {
        Sid    = "eks"
        Effect = "Allow"
        Principal = {
          Federated = "arn:aws:iam::************:oidc-provider/oidc.eks.ap-southeast-1.amazonaws.com/id/C0C7EFCE1387FA36CE090693EEB90BAB"
        }
        Action = "sts:AssumeRoleWithWebIdentity"
        Condition = {
          "StringEquals" = {
            "oidc.eks.ap-southeast-1.amazonaws.com/id/C0C7EFCE1387FA36CE090693EEB90BAB:aud" = "sts.amazonaws.com"
          }
          "StringEquals" = {
            "oidc.eks.ap-southeast-1.amazonaws.com/id/C0C7EFCE1387FA36CE090693EEB90BAB:sub" = "system:serviceaccount:${var.service_namespace}:blinkit-${var.iam_prefix}-${var.environment}-eks-role"
          }
        }
      },
      {
        Sid    = "selfAssume"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::************:role/blinkit-${var.iam_prefix}-${var.environment}-eks-role"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
  tags = merge(local.common_tags,
  { "Name" : "blinkit-prod-${var.iam_prefix}-${var.environment}-eks-role" })
}

resource "aws_iam_policy" "role-policy" {
  name = "blinkit-prod-${var.iam_prefix}-${var.environment}-eks-sa-policy"
  path = "/"

  tags = merge(local.common_tags,
  { "Name" : "blinkit-prod-${var.iam_prefix}-${var.environment}-eks-sa-policy" })

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "ServiceAccountS3Access"
        Effect = "Allow"
        Action = [
          "s3:Get*",
          "s3:List*",
          "s3:Put*",
          "s3:Delete*"
        ]
        Resource = [
          "arn:aws:s3:::blinkit-preprod-pinot-fs",
          "arn:aws:s3:::blinkit-preprod-pinot-fs/*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "role-policy-attachment" {
  role       = aws_iam_role.serviceaccount-role.name
  policy_arn = aws_iam_policy.role-policy.arn
}

# Create a policy for EKS Worker Role to access the S3 bucket
resource "aws_iam_policy" "eks-worker-s3-policy" {
  name        = "eks-preprod-worker-pinot-s3-access-policy"
  description = "Policy allowing EKS Worker Role to access Pinot S3 bucket"
  
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "EKSWorkerS3Access"
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket",
          "s3:GetBucketLocation",
          "s3:AbortMultipartUpload",
          "s3:ListMultipartUploadParts"
        ]
        Resource = [
          "arn:aws:s3:::blinkit-preprod-pinot-fs",
          "arn:aws:s3:::blinkit-preprod-pinot-fs/*"
        ]
      }
    ]
  })
}

# Attach the policy to the EKS Worker Role
resource "aws_iam_role_policy_attachment" "eks-worker-s3-policy-attachment" {
  role       = "eks-preprod-apse1-analytics-EKS-Worker-Role"
  policy_arn = aws_iam_policy.eks-worker-s3-policy.arn
}