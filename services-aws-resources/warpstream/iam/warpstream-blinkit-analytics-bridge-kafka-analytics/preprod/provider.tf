provider "aws" {
  region  = "ap-southeast-1"
  profile = "blinkit_analytics"
}

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.4.0"
    }
  }
  backend "s3" {
    bucket  = "blinkit-analytics-terraform"
    key     = "banalytics-infrastructure/blinkit-stack/services-aws-resources/warpstream/iam/warpstream-blinkit-analytics-bridge-kafka-analytics-preprod.tfstate"
    region  = "ap-southeast-1"
    profile = "blinkit_analytics"
  }
}