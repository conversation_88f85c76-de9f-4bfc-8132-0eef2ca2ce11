# If planning to change the name then ensure it get updated on condition block too.
resource "aws_iam_role" "serviceaccount-role" {
  name               = "banalytics-${var.iam_prefix}-${var.environment}-eks-role" #update banalytics from blinkit-analytics due to 64 chars limit on iam role name
  assume_role_policy = data.aws_iam_policy_document.trusted_policy_doc.json
  tags = merge(local.common_tags,
  { "Name" : "banalytics-${var.iam_prefix}-${var.environment}-eks-role" }) #update banalytics from blinkit-analytics due to 64 chars limit on iam role name
}

data "aws_iam_policy_document" "trusted_policy_doc" {
  statement {
    sid     = "EksAnalyticsApse1Preprod"
    effect  = "Allow"
    actions = ["sts:AssumeRoleWithWebIdentity"]

    principals {
      type        = "Federated"
      identifiers = ["arn:aws:iam::************:oidc-provider/oidc.eks.ap-southeast-1.amazonaws.com/id/43262DA6034AF6C2122E5BC40AAE70C3"]
    }

    condition {
      test     = "StringEquals"
      variable = "oidc.eks.ap-southeast-1.amazonaws.com/id/43262DA6034AF6C2122E5BC40AAE70C3:aud"
      values   = ["sts.amazonaws.com"]
    }

    condition {
      test     = "StringEquals"
      variable = "oidc.eks.ap-southeast-1.amazonaws.com/id/43262DA6034AF6C2122E5BC40AAE70C3:sub"
      values   = ["system:serviceaccount:${var.service_namespace}:banalytics-${var.iam_prefix}-${var.environment}-eks-role"] #update banalytics from blinkit-analytics due to 64 chars limit on iam role name
    }
  }
}

resource "aws_iam_policy" "role-policy" {
  name = "banalytics-${var.iam_prefix}-${var.environment}-eks-sa-policy" #update banalytics from blinkit-analytics due to 64 chars limit on iam role name
  path = "/"

  tags = merge(local.common_tags,
  { "Name" : "banalytics-${var.iam_prefix}-${var.environment}-eks-sa-policy" }) #update banalytics from blinkit-analytics due to 64 chars limit on iam role name

  policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
          "Sid": "AllowS3",
          "Effect": "Allow",
          "Action": [
            "s3:PutObject",
            "s3:GetObject",
            "s3:DeleteObject",
            "s3:ListBucket"
          ],
          "Resource": [
            "arn:aws:s3:::blinkit-warpstream-preprod-bridge-analytics",
            "arn:aws:s3:::blinkit-warpstream-preprod-bridge-analytics/*"
          ]
        }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "role-policy-attachment" {
  role       = aws_iam_role.serviceaccount-role.name
  policy_arn = aws_iam_policy.role-policy.arn
}
