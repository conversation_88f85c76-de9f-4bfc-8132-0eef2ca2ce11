variable "iam_prefix" {
  description = "iam prefix. As iam name will be combination `iam_prefix-environment`"
  type        = string
}

variable "service_name" {
  description = "Service Name"
  type        = string
}

variable "service_namespace" {
  description = "Service Namespace"
  type        = string
}

variable "environment" {
  description = "Name of environment: primary, canary, stage, etc"
  type        = string
}