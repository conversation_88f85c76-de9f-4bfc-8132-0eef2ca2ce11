# IAM Roles for Hive Meta Store preprod deployment
# Hive Meta Store Service Role with specific permissions

# Trust policy for Hive Meta Store Service role
resource "aws_iam_role" "hive_meta_store_role" {
  name = "blinkit-${var.environment}-analytics-${var.iam_prefix}-primary-eks-role"
  path = "/services/${var.service_name}/"

  # Trust policy allowing EKS service account to assume this role via OIDC
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "EKSOIDCAssumeRole"
        Effect = "Allow"
        Principal = {
          Federated = "arn:aws:iam::${var.aws_account_id}:oidc-provider/oidc.eks.${var.aws_provider_region}.amazonaws.com/id/43262DA6034AF6C2122E5BC40AAE70C3"
        }
        Action = "sts:AssumeRoleWithWebIdentity"
        Condition = {
          StringEquals = {
            "oidc.eks.${var.aws_provider_region}.amazonaws.com/id/43262DA6034AF6C2122E5BC40AAE70C3:aud" = "sts.amazonaws.com"
            "oidc.eks.${var.aws_provider_region}.amazonaws.com/id/43262DA6034AF6C2122E5BC40AAE70C3:sub" = "system:serviceaccount:data:blinkit-${var.environment}-${var.iam_prefix}-primary-eks-role"
          }
        }
      }
    ]
  })

  tags = merge(local.common_tags, {
    "cost:application"       = var.service_name
    "cost:component"         = "iam"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  })
}

# S3 Policy for Hive Meta Store
resource "aws_iam_policy" "hive_meta_store_s3_policy" {
  name        = "blinkit-${var.environment}-analytics-${var.iam_prefix}-s3-policy"
  path        = "/services/${var.service_name}/"
  description = "S3 access policy for Hive Meta Store service"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:Get*",
          "s3:List*"
        ]
        Resource = [
          "arn:aws:s3:::ahana-grofers-prod-data-dwh-*",
          "arn:aws:s3:::ahana-grofers-prod-data-dwh-*/*",
          "arn:aws:s3:::blinkit-cloudflare-logs",
          "arn:aws:s3:::blinkit-cloudflare-logs/*",
          "arn:aws:s3:::blinkit-data-pipeline",
          "arn:aws:s3:::blinkit-data-pipeline/*",
          "arn:aws:s3:::blinkit-data-staging",
          "arn:aws:s3:::blinkit-data-staging/*",
          "arn:aws:s3:::blinkit-derived-etls",
          "arn:aws:s3:::blinkit-derived-etls/*",
          "arn:aws:s3:::blinkit-jumbo",
          "arn:aws:s3:::derived-etls",
          "arn:aws:s3:::grofers-business-intelligence",
          "arn:aws:s3:::grofers-business-intelligence/*",
          "arn:aws:s3:::grofers-prod-dse-sgp",
          "arn:aws:s3:::grofers-prod-dse-sgp/*",
          "arn:aws:s3:::grofers-test-dse",
          "arn:aws:s3:::grofers-test-dse/*",
          "arn:aws:s3:::blinkit-data-spark-production",
          "arn:aws:s3:::blinkit-data-spark-production/*",
          "arn:aws:s3:::grofers-test-dse-singapore",
          "arn:aws:s3:::grofers-test-dse-singapore/*",
          "arn:aws:s3:::prod-data-feature-store",
          "arn:aws:s3:::prod-data-feature-store/*",
          "arn:aws:s3:::prod-data-lake-derived-tables",
          "arn:aws:s3:::prod-data-lake-derived-tables/*",
          "arn:aws:s3:::prod-data-lake-hudi-applications",
          "arn:aws:s3:::prod-data-lake-hudi-applications/*",
          "arn:aws:s3:::prod-data-lake-hudi-branch-events",
          "arn:aws:s3:::prod-data-lake-hudi-branch-events/*",
          "arn:aws:s3:::prod-data-lake-hudi-rudder-events",
          "arn:aws:s3:::prod-data-lake-hudi-rudder-events/*",
          "arn:aws:s3:::prod-data-lake-hudi-segment-events",
          "arn:aws:s3:::prod-data-lake-hudi-segment-events/*",
          "arn:aws:s3:::prod-data-presto-adhoc",
          "arn:aws:s3:::prod-data-presto-adhoc/*",
          "arn:aws:s3:::prod-dse-backend-events",
          "arn:aws:s3:::prod-dse-backend-events/*",
          "arn:aws:s3:::prod-dse-backend-events-raw",
          "arn:aws:s3:::prod-dse-backend-events-raw/*",
          "arn:aws:s3:::prod-dse-branchio-archive",
          "arn:aws:s3:::prod-dse-branchio-archive/*",
          "arn:aws:s3:::prod-dse-datalake",
          "arn:aws:s3:::prod-dse-datalake/*",
          "arn:aws:s3:::prod-dse-projects",
          "arn:aws:s3:::prod-dse-projects/*",
          "arn:aws:s3:::prod-dse-redash-data-store",
          "arn:aws:s3:::prod-dse-redash-data-store/*",
          "arn:aws:s3:::prod-dse-redshift-adhoc",
          "arn:aws:s3:::prod-dse-redshift-adhoc/*",
          "arn:aws:s3:::prod-dse-rudder-datetime-chunks-sgp",
          "arn:aws:s3:::prod-dse-rudder-datetime-chunks-sgp/*",
          "arn:aws:s3:::prod-dse-spark-etl-archive-sgp",
          "arn:aws:s3:::prod-dse-spark-etl-archive-sgp/*",
          "arn:aws:s3:::prod-dse-spark-etl-processed-sgp",
          "arn:aws:s3:::prod-dse-spark-etl-processed-sgp/*",
          "arn:aws:s3:::prod-data-clevertap-sg",
          "arn:aws:s3:::prod-data-clevertap-sg/*",
          "arn:aws:s3:::ap-southeast-1-442534439095-cubiscan-sftp-data",
          "arn:aws:s3:::ap-southeast-1-442534439095-cubiscan-sftp-data/*"
        ]
      }
    ]
  })

  tags = merge(local.common_tags, {
    "cost:application"       = var.service_name
    "cost:component"         = "iam"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  })
}

# Attach the S3 policy to the role
resource "aws_iam_role_policy_attachment" "hive_meta_store_s3_policy_attachment" {
  role       = aws_iam_role.hive_meta_store_role.name
  policy_arn = aws_iam_policy.hive_meta_store_s3_policy.arn
}
