variable "service_name" {
  type = string
}

variable "environment" {
  type    = string
  default = "preprod"
}

variable "aws_provider_region" {
  type = string
}

variable "aws_account_id" {
  type        = string
  description = "AWS Account ID"
}

variable "aws_profile" {
  type = string
}

variable "iam_prefix" {
  type        = string
  description = "Prefix for IAM resources"
}

variable "service_namespace" {
  type        = string
  description = "Namespace for cost allocation"
}