# IAM Roles for CDP prod deployment
# 1. CDP Service IAM Role
# Trust policy for CDP Service role
resource "aws_iam_role" "cdp_service_role" {
  name = "${var.environment}-${var.service_name}-service-role"
  path = "/services/${var.service_name}/"

  # Trust policy as a JSON string
  assume_role_policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "defaultec2",
            "Effect": "Allow",
            "Principal": {
                "Service": "ec2.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        },
        {
            "Sid": "SelfAssume",
            "Effect": "Allow",
            "Principal": {
                "AWS": "arn:aws:iam::${var.aws_account_id}:role/services/${var.service_name}/${var.environment}-${var.service_name}-service-role"
            },
            "Action": "sts:AssumeRole"
        },
        {
            "Sid": "eks",
            "Effect": "Allow",
            "Principal": {
                "Federated": "arn:aws:iam::${var.aws_account_id}:oidc-provider/oidc.eks.${var.aws_provider_region}.amazonaws.com/id/C0C7EFCE1387FA36CE090693EEB90BAB"
            },
            "Action": "sts:AssumeRoleWithWebIdentity",
            "Condition": {
                "StringEquals": {
                    "oidc.eks.${var.aws_provider_region}.amazonaws.com/id/C0C7EFCE1387FA36CE090693EEB90BAB:aud": "sts.amazonaws.com"
                },
                "ForAnyValue:StringEquals": {
                    "oidc.eks.${var.aws_provider_region}.amazonaws.com/id/C0C7EFCE1387FA36CE090693EEB90BAB:sub": "system:serviceaccount:${var.environment}-${var.service_name}:${var.environment}-${var.service_name}-service-role-sa"
                }
            }
        }
    ]
}
EOF

  tags = {
    "cost:application"      = var.service_name
    "cost:component"        = "iam"
    "cost:environment"      = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  }
}

# Create an EMR policy for CDP Service
resource "aws_iam_policy" "cdp_emr_policy" {
  name        = "cdp-emr-policy-${var.environment}"
  description = "Policy for CDP service to manage EMR clusters"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = [
          "elasticmapreduce:RunJobFlow",
          "elasticmapreduce:CreateCluster",
          "elasticmapreduce:DescribeCluster",
          "elasticmapreduce:TerminateJobFlows",
          "elasticmapreduce:ListClusters",
          "elasticmapreduce:AddJobFlowSteps",
          "elasticmapreduce:DescribeStep",
          "elasticmapreduce:ListSteps",
          "elasticmapreduce:CancelSteps",
          "elasticmapreduce:AddTags"
        ],
        Resource = "*"
      },
      {
        Effect   = "Allow",
        Action   = [
          "iam:PassRole"
        ],
        Resource = [
          "arn:aws:iam::*:role/*EMR*",
          "arn:aws:iam::183295456051:role/prod-blinkit-cdp-emr-role"
        ]
      }
    ]
  })
}

# Attach the EMR policy to the CDP Service role
resource "aws_iam_role_policy_attachment" "cdp_emr_policy_attachment" {
  role       = aws_iam_role.cdp_service_role.name
  policy_arn = aws_iam_policy.cdp_emr_policy.arn
}

# Create an S3 policy for CDP Service
resource "aws_iam_policy" "cdp_s3_policy" {
  name        = "${var.service_namespace}-${var.environment}-s3-policy"
  description = "Policy for CDP service to access S3 resources"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "S3ReadAccessForCDPService",
        Effect = "Allow",
        Action = [
          "s3:GetObject",
          "s3:GetObjectVersion",
          "s3:GetObjectAcl",
          "s3:GetObjectVersionAcl",
          "s3:ListBucket",
          "s3:GetBucketLocation",
          "s3:GetBucketVersioning"
        ],
        Resource = [
          "arn:aws:s3:::blinkit-analytics",
          "arn:aws:s3:::blinkit-analytics/config/*"
        ]
      },
      {
        Sid    = "S3WriteAccessForCDPService",
        Effect = "Allow",
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl",
          "s3:PutObjectVersionAcl",
          "s3:DeleteObject",
          "s3:DeleteObjectVersion",
          "s3:AbortMultipartUpload",
          "s3:ListMultipartUploadParts"
        ],
        Resource = [
          "arn:aws:s3:::blinkit-analytics/config/*"
        ]
      }
    ]
  })
}

# Attach the S3 policy to the CDP Service role
resource "aws_iam_role_policy_attachment" "cdp_s3_policy_attachment" {
  role       = aws_iam_role.cdp_service_role.name
  policy_arn = aws_iam_policy.cdp_s3_policy.arn
} 