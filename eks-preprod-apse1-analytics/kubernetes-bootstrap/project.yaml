apiVersion: v1
items:
- apiVersion: argoproj.io/v1alpha1
  kind: AppProject
  metadata:
    name: data-manifests
    namespace: argocd
  spec:
    destinations:
    - namespace: '!default'
      server: '*'
    - namespace: '!kube-system'
      server: '*'
    - namespace: '!monitoring'
      server: '*'
    - namespace: '!infra'
      server: '*'
    namespaceResourceWhitelist:
    - group: '*'
      kind: '*'
    sourceRepos:
    - https://github.com/grofers/kube-data-infra.git
- apiVersion: argoproj.io/v1alpha1
  kind: AppProject
  metadata:
    name: infrastructure
    namespace: argocd
  spec:
    clusterResourceWhitelist:
    - group: '*'
      kind: '*'
    destinations:
    - name: '*'
      namespace: '*'
      server: '*'
    sourceRepos:
    - https://github.com/grofers/kube-data-infra.git
kind: List
metadata: {}
