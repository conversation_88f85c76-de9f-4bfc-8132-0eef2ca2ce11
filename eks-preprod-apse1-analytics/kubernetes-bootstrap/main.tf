data "aws_eks_cluster" "this" {
  name = var.cluster_name
}


module "kubernetes-bootstrap" {
  source = "git::https://github.com/grofers/terraform-registry//modules/blinkit-stack/kubernetes-bootstrap?ref=eks-stack-updates"
  argocd = var.argocd
  vault  = var.vault
  eks = {
    cluster_name    = var.cluster_name
    kubernetes_host = data.aws_eks_cluster.this.endpoint
    kubernetes_ca   = base64decode(data.aws_eks_cluster.this.certificate_authority[0].data)
  }
}
