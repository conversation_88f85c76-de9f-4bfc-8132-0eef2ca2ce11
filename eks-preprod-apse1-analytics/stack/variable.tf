variable "default_ami_id" {
  type = string
}

variable "cluster_name" {
  type = string
}

variable "cluster_version" {
  type = string
}

variable "cluster_service_ip_cidr" {
  type = string
}

variable "cluster_env" {
  type = string
}

variable "vpc_config" {
  type = any
}

variable "node_additional_security_groups" {
  type    = list(string)
  default = null
}

variable "ocean_config" {
  type = any
}

variable "infra_nodegroup" {
  type = any
}
