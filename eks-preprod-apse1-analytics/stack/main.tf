data "vault_kv_secret" "spotinst_creds" {
  path = "infra/atlantis/spotinst"
}

module "this" {
  source = "git::https://github.com/grofers/terraform-registry//modules/blinkit-stack/eks?ref=eks-stack-updates"

  cluster_name   = var.cluster_name
  cluster_env    = var.cluster_env
  default_ami_id = var.default_ami_id

  ocean_config = {
    spotinst_token             = data.vault_kv_secret.spotinst_creds.data.spotinst_token
    spotinst_account           = data.vault_kv_secret.spotinst_creds.data.spotinst_account
    additional_security_groups = var.node_additional_security_groups
    autoscaler                 = var.ocean_config.autoscaler
    whitelist_ec2_types        = var.ocean_config.whitelist_ec2_types
    root_volume_size           = var.ocean_config.root_volume_size
    key_name                   = var.ocean_config.key_name
  }

  infra_nodegroup = var.infra_nodegroup

  eks_configuration = {
    version      = var.cluster_version
    service_cidr = var.cluster_service_ip_cidr
  }

  infra_driver_role = {
    extra_serviceaccounts = [
      {
        name      = "blinkit-prometheus-thanos-stack"
        namespace = "monitoring"
      }
    ]
    thanos_bucket_name = "eks-prod-apse1-thanos"
  }

  vpc_config = var.vpc_config
}