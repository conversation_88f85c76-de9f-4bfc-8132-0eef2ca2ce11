## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | ~> v1.3.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | 5.38.0 |
| <a name="requirement_spotinst"></a> [spotinst](#requirement\_spotinst) | 1.161.0 |
| <a name="requirement_vault"></a> [vault](#requirement\_vault) | 3.25.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_vault"></a> [vault](#provider\_vault) | 3.25.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_this"></a> [this](#module\_this) | **************:grofers/terraform-registry//modules/blinkit-stack/eks | dr-module |

## Resources

| Name | Type |
|------|------|
| [vault_kv_secret.spotinst_creds](https://registry.terraform.io/providers/hashicorp/vault/3.25.0/docs/data-sources/kv_secret) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_cluster_env"></a> [cluster\_env](#input\_cluster\_env) | n/a | `string` | n/a | yes |
| <a name="input_cluster_name"></a> [cluster\_name](#input\_cluster\_name) | n/a | `string` | n/a | yes |
| <a name="input_cluster_service_ip_cidr"></a> [cluster\_service\_ip\_cidr](#input\_cluster\_service\_ip\_cidr) | n/a | `string` | n/a | yes |
| <a name="input_cluster_version"></a> [cluster\_version](#input\_cluster\_version) | n/a | `string` | n/a | yes |
| <a name="input_default_ami_id"></a> [default\_ami\_id](#input\_default\_ami\_id) | n/a | `string` | n/a | yes |
| <a name="input_infra_nodegroup"></a> [infra\_nodegroup](#input\_infra\_nodegroup) | n/a | <pre>object({<br>    instance_types   = optional(list(string), ["r5.2xlarge"])<br>    min_size         = optional(number, 3)<br>    max_size         = optional(number, 6)<br>    root_volume_size = optional(number, 120)<br>  })</pre> | n/a | yes |
| <a name="input_node_additional_security_groups"></a> [node\_additional\_security\_groups](#input\_node\_additional\_security\_groups) | n/a | `list(string)` | `null` | no |
| <a name="input_ocean_config"></a> [ocean\_config](#input\_ocean\_config) | n/a | <pre>object({<br>    whitelist_ec2_types = list(string)<br>    root_volume_size    = optional(number, 120)<br>    autoscaler = object({<br>      autoscale_is_enabled                 = optional(bool, true)<br>      autoscale_is_auto_config             = optional(bool, true)<br>      autoscale_cooldown                   = optional(number, null)<br>      auto_headroom_percentage             = optional(number, 5)<br>      enable_automatic_and_manual_headroom = optional(bool, null)<br>      max_scale_down_percentage            = optional(number, 10)<br>      resource_limits = object({<br>        max_vcpu       = optional(number, 20000)<br>        max_memory_gib = optional(number, 100000)<br>      })<br>    })<br>  })</pre> | n/a | yes |
| <a name="input_vpc_config"></a> [vpc\_config](#input\_vpc\_config) | n/a | <pre>object({<br>    id              = string<br>    public_subnets  = list(string)<br>    private_subnets = list(string)<br>    region          = string<br>    cidr_ranges     = list(string)<br>  })</pre> | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_cluster_name"></a> [cluster\_name](#output\_cluster\_name) | EKS Controlplane name |
| <a name="output_eks_infra_driver_role"></a> [eks\_infra\_driver\_role](#output\_eks\_infra\_driver\_role) | AWS IAM role arn for infra services |
| <a name="output_eks_worker_role"></a> [eks\_worker\_role](#output\_eks\_worker\_role) | AWS IAM role arn for EKS nodegroup |
| <a name="output_eks_worker_role_profile"></a> [eks\_worker\_role\_profile](#output\_eks\_worker\_role\_profile) | Profile arn of EKS Worker IAM role |
| <a name="output_node_security_group"></a> [node\_security\_group](#output\_node\_security\_group) | Security group for nodegroups |
| <a name="output_ocean_id"></a> [ocean\_id](#output\_ocean\_id) | Ocean-id of cluster on Spotinst |
| <a name="output_shared_security_group"></a> [shared\_security\_group](#output\_shared\_security\_group) | Shared Security group. To be attached with nodegroups. |
