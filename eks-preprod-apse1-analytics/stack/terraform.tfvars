cluster_name            = "eks-preprod-apse1-analytics"
cluster_env             = "preprod"
cluster_version         = "1.31"
cluster_service_ip_cidr = "**********/16"
default_ami_id          = "ami-0e9a0dcd86bf5016c"
vpc_config = {
  id              = "vpc-0a1245931c5ad6a4c"
  private_subnets = ["subnet-088a1566c712c2761", "subnet-03e86d09ca089a9b2", "subnet-0f1b127fddfca6e13"]
  region          = "ap-southeast-1"
  cidr_ranges     = ["*********/16"]
}

ocean_config = {
  autoscaler = {
    autoscale_is_enabled                 = true
    autoscale_is_auto_config             = true
    max_scale_down_percentage            = 2
    auto_headroom_percentage             = 0
    enable_automatic_and_manual_headroom = true
    resource_limits = {
      max_vcpu : 20000
      max_memory_gib : 100000
    }
  }
  whitelist_ec2_types = ["c6gn.xlarge", "c6gn.large", "c5.9xlarge", "c5.4xlarge", "c5.2xlarge", "c5a.4xlarge", "c5a.2xlarge", "c5a.8xlarge", "c5ad.4xlarge", "c5ad.2xlarge", "c5ad.8xlarge", "c5d.4xlarge", "c5d.9xlarge", "c5n.4xlarge", "c5n.2xlarge", "c5n.9xlarge", "c6a.4xlarge", "c6a.2xlarge", "c6a.8xlarge", "c6i.4xlarge", "c6i.8xlarge", "c6i.2xlarge", "c6id.4xlarge", "c6id.8xlarge", "c6id.2xlarge", "c6in.8xlarge", "c6in.2xlarge", "c6in.4xlarge", "c7g.8xlarge", "c7g.2xlarge", "c7i.4xlarge", "c7i.2xlarge", "c7i.8xlarge", "d2.4xlarge", "d3.4xlarge", "g2.8xlarge", "g3.4xlarge", "g4dn.4xlarge", "g4dn.8xlarge", "hs1.8xlarge", "i2.4xlarge", "i3.4xlarge", "inf1.6xlarge", "m5.8xlarge", "m5.2xlarge", "m5.4xlarge", "m5a.4xlarge", "m5d.8xlarge", "m5d.4xlarge", "m5dn.8xlarge", "m5dn.4xlarge", "m5n.4xlarge", "m5n.8xlarge", "m5zn.6xlarge", "m6i.8xlarge", "m6i.4xlarge", "m7g.4xlarge", "m7g.2xlarge", "m7i.4xlarge", "m7i.8xlarge", "m7i.2xlarge", "r4.4xlarge", "r5.8xlarge", "r5.4xlarge", "r5b.4xlarge", "r5d.4xlarge", "r5dn.4xlarge", "r5n.4xlarge", "r5n.2xlarge", "r6i.4xlarge", "r6i.12xlarge", "r6i.16xlarge", "r6i.8xlarge", "r7g.2xlarge", "r7g.xlarge", "r7i.2xlarge", "r7i.4xlarge", "r7i.8xlarge", "r6in.2xlarge", "m6in.8xlarge", "m6in.4xlarge", "r6in.4xlarge", "c6g.16xlarge", "c6g.2xlarge", "c6g.4xlarge", "c6g.12xlarge", "c6g.8xlarge", "c6gn.16xlarge", "c6gn.4xlarge", "c6gn.2xlarge", "c6gn.12xlarge", "c6gn.8xlarge", "c7g.12xlarge", "c7g.16xlarge", "c7g.4xlarge", "m6g.12xlarge", "m6g.4xlarge", "m6g.2xlarge", "m6g.16xlarge", "m6g.8xlarge", "m7g.8xlarge", "m7g.16xlarge", "m7g.12xlarge", "r6g.4xlarge", "r6g.16xlarge", "r6g.12xlarge", "r6g.8xlarge", "r6g.2xlarge", "r7g.12xlarge", "r7g.8xlarge", "r7g.16xlarge", "r7g.4xlarge"]
  key_name            = "blinkit-analytics-eks"
  root_volume_size    = 120
}

infra_nodegroup = {
  instance_types = ["c5.2xlarge", "c5.4xlarge", "c5.2xlarge", "c5n.4xlarge", "c5n.2xlarge"]
  min_size       = 3
  max_size       = 15
  subnets        = ["subnet-088a1566c712c2761", "subnet-03e86d09ca089a9b2", "subnet-0f1b127fddfca6e13"]
  key_name       = "blinkit-analytics-eks"
  ami_id         = "ami-0e9a0dcd86bf5016c"
}
