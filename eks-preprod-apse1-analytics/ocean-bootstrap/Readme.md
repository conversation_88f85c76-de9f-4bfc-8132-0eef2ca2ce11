## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | ~> v1.3.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | 5.38.0 |
| <a name="requirement_kubernetes"></a> [kubernetes](#requirement\_kubernetes) | 2.26.0 |
| <a name="requirement_vault"></a> [vault](#requirement\_vault) | 3.25.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 5.38.0 |
| <a name="provider_vault"></a> [vault](#provider\_vault) | 3.25.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_ocean-controller"></a> [ocean-controller](#module\_ocean-controller) | **************:grofers/terraform-registry//modules/blinkit-stack/ocean-controller | dr-module |

## Resources

| Name | Type |
|------|------|
| [aws_eks_cluster.this](https://registry.terraform.io/providers/hashicorp/aws/5.38.0/docs/data-sources/eks_cluster) | data source |
| [vault_kv_secret.spotinst_creds](https://registry.terraform.io/providers/hashicorp/vault/3.25.0/docs/data-sources/kv_secret) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_cluster_env"></a> [cluster\_env](#input\_cluster\_env) | n/a | `string` | n/a | yes |
| <a name="input_cluster_name"></a> [cluster\_name](#input\_cluster\_name) | n/a | `string` | n/a | yes |

## Outputs

No outputs.
