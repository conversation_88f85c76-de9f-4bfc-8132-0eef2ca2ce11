terraform {
  required_version = "~> v1.1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.38.0"
    }
    vault = {
      source  = "hashicorp/vault"
      version = "3.25.0"
    }
  }

  backend "s3" {
    bucket  = "blinkit-analytics-terraform"
    key     = "banalytics-infrastructure/blinkit-stack/eks-preprod-apse1-analytics/vault.tfstate"
    region  = "ap-southeast-1"
    profile = "blinkit_analytics"
  }
}

provider "aws" {
  region  = "ap-southeast-1"
  profile = "blinkit_analytics"
}

provider "vault" {
  address          = "https://vault.${var.cluster_env}.blinkit.dev"
  skip_child_token = true
  auth_login_aws {
    role        = "atlantis"
    mount       = "aws-analytics"
    aws_profile = "blinkit_analytics"
  }
}
