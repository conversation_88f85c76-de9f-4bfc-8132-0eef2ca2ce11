## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | ~> v1.3.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | 5.38.0 |
| <a name="requirement_spotinst"></a> [spotinst](#requirement\_spotinst) | 1.161.0 |
| <a name="requirement_vault"></a> [vault](#requirement\_vault) | 3.25.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 5.38.0 |
| <a name="provider_vault"></a> [vault](#provider\_vault) | 3.25.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_default_ng"></a> [default\_ng](#module\_default\_ng) | **************:grofers/terraform-registry//modules/blinkit-stack/ocean | dr-module |
| <a name="module_kong_ng"></a> [kong\_ng](#module\_kong\_ng) | **************:grofers/terraform-registry//modules/blinkit-stack/ocean | dr-module |
| <a name="module_monitoring_ng"></a> [monitoring\_ng](#module\_monitoring\_ng) | **************:grofers/terraform-registry//modules/blinkit-stack/ocean | dr-module |

## Resources

| Name | Type |
|------|------|
| [aws_subnet.this](https://registry.terraform.io/providers/hashicorp/aws/5.38.0/docs/data-sources/subnet) | data source |
| [vault_kv_secret.spotinst_creds](https://registry.terraform.io/providers/hashicorp/vault/3.25.0/docs/data-sources/kv_secret) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_cluster_env"></a> [cluster\_env](#input\_cluster\_env) | n/a | `string` | `"prod"` | no |
| <a name="input_cluster_name"></a> [cluster\_name](#input\_cluster\_name) | n/a | `string` | n/a | yes |
| <a name="input_cluster_version"></a> [cluster\_version](#input\_cluster\_version) | n/a | `string` | n/a | yes |
| <a name="input_default_ami_id"></a> [default\_ami\_id](#input\_default\_ami\_id) | n/a | `string` | n/a | yes |
| <a name="input_iam_instance_profile"></a> [iam\_instance\_profile](#input\_iam\_instance\_profile) | n/a | `string` | n/a | yes |
| <a name="input_ocean_id"></a> [ocean\_id](#input\_ocean\_id) | n/a | `string` | `null` | no |
| <a name="input_private_subnets"></a> [private\_subnets](#input\_private\_subnets) | n/a | `list(string)` | n/a | yes |
| <a name="input_shared_security_group"></a> [shared\_security\_group](#input\_shared\_security\_group) | n/a | `string` | n/a | yes |
| <a name="input_worker_security_group"></a> [worker\_security\_group](#input\_worker\_security\_group) | n/a | `string` | n/a | yes |

## Outputs

No outputs.
