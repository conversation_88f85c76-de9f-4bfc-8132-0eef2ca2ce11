cluster_name          = "eks-preprod-apse1-analytics"
cluster_env           = "preprod"
cluster_version       = "1.31"
ocean_id              = "o-bef6fdc5"
private_subnets       = ["subnet-088a1566c712c2761", "subnet-03e86d09ca089a9b2", "subnet-0f1b127fddfca6e13"]
default_ami_id        = "ami-0e9a0dcd86bf5016c"
default_ami_id_arm64  = "ami-0a16f9a3eb50d369e"
worker_security_group = "sg-054cd4ebf351b71ee"
shared_security_group = "sg-0a648d3a478f5c5f8"
iam_instance_profile  = "arn:aws:iam::183295456051:instance-profile/eks-preprod-apse1-analytics-EKS-Worker-Role"
key_name              = "blinkit-analytics-eks"