variable "environment" {
  type        = string
  description = <<DESC
    The environment for this service. Value should be one of `dev` or `prod`
    DESC
  default     = "preprod"
}

variable "aws_provider_region" {
  type = string
}

variable "arm_ami_id" {
  type = string
}

variable "amd_ami_id" {
  type = string
}

variable "aws_profile" {
  type = string
}

variable "env" {
  type = string
}

variable "vpc_id" {
  type        = string
  description = "The ID of the VPC where Trino will be deployed"
}
