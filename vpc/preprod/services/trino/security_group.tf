resource "aws_security_group" "preprod_trino_sg" {
  name        = "preprod-trino-security-group"
  description = "Security group for preprod Trino in the new VPC"
  vpc_id      = var.vpc_id

  ingress {
    description = "TLS - blinkit-preprod account"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["********/16", "*********/16"]
  }
  ingress {
    description = "Allow HTTP traffic from complete VPC"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["*********/16"]
  }

  ingress {
    description = "blinkit-preprod account - port 8889"
    from_port   = 8889
    to_port     = 8889
    protocol    = "tcp"
    cidr_blocks = ["********/16", "*********/16"]
  }

  ingress {
    description = "SSH access - blinkit-preprod account"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["********/16", "*********/16"]
  }

  ingress {
    description = "Port 1811 - blinkit-preprod account"
    from_port   = 1811
    to_port     = 1811
    protocol    = "tcp"
    cidr_blocks = ["********/16", "*********/16"]
  }

  ingress {
    description = "Application connector - blinkit-preprod account"
    from_port   = 8090
    to_port     = 8090
    protocol    = "tcp"
    cidr_blocks = ["********/16", "*********/16"]
  }

  ingress {
    description = "Admin connector - blinkit-preprod account"
    from_port   = 8091
    to_port     = 8091
    protocol    = "tcp"
    cidr_blocks = ["********/16", "*********/16"]
  }

  ingress {
    description = "SMTP - blinkit-preprod account"
    from_port   = 587
    to_port     = 587
    protocol    = "tcp"
    cidr_blocks = ["********/16", "*********/16"]
  }

  ingress {
    description = "Allow all traffic within the security group"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    self        = true
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "preprod-trino-security-group"
    Environment = "preprod"
  }
}
