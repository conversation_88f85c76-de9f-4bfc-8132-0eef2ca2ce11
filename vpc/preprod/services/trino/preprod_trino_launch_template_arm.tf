resource "aws_launch_template" "preprod_trino_lt_arm" {
  name      = "preprod-trino-arm-lt-1"
  image_id  = data.aws_ami.ecs_arm_ami.id
  key_name  = "prod-trino"

  vpc_security_group_ids = [
    aws_security_group.preprod_trino_sg.id,
  ]

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "true"
      iops                  = 3000
      snapshot_id           = "snap-017e2cc967b917280"
      throughput            = 125
      volume_size           = 256
      volume_type           = "gp3"
    }
  }

  iam_instance_profile {
    arn = data.aws_iam_instance_profile.ecs_instance_role.arn
  }

  monitoring {
    enabled = true
  }

  default_version = 3

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name = "preprod-trino-arm"
      Environment = "preprod"
    }
  }
}
