data "aws_subnet" "trino_subnet_1" {
  id = "subnet-0f1b127fddfca6e13"
}

data "aws_subnet" "trino_subnet_2" {
  id = "subnet-03e86d09ca089a9b2"
}

data "aws_subnet" "trino_subnet_3" {
  id = "subnet-088a1566c712c2761"
}

data "aws_ami" "ecs_amd_ami" {
  most_recent = true
  owners      = ["self", "amazon"]
  filter {
    name   = "image-id"
    values = [var.amd_ami_id]
  }
}

data "aws_ami" "ecs_arm_ami" {
  most_recent = true
  owners      = ["self", "amazon"]
  filter {
    name   = "image-id"
    values = [var.arm_ami_id]
  }
}

data "aws_iam_instance_profile" "ecs_instance_role" {
  name = "ecsInstanceRole"
}

