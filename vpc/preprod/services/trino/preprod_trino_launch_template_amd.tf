resource "aws_launch_template" "preprod_trino_lt_amd" {
  name                   = "preprod-trino-amd-lt-1"
  image_id               = data.aws_ami.ecs_amd_ami.id

  vpc_security_group_ids = [
    aws_security_group.preprod_trino_sg.id,
  ]

  key_name               = "prod-trino"

  iam_instance_profile {
    arn = data.aws_iam_instance_profile.ecs_instance_role.arn
  }

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "true"
      iops                  = 3000
      snapshot_id           = "snap-02a032375f7996f63"
      throughput            = 125
      volume_size           = 256
      volume_type           = "gp3"
    }
  }

  monitoring {
    enabled = true
  }

  default_version = 3

  tag_specifications {
    resource_type = "instance"
    tags = {
      Name = "preprod-trino-amd"
      Environment = "preprod"
    }
  }
}
