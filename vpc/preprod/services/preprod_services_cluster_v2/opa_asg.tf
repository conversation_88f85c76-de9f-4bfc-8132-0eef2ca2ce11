resource "aws_autoscaling_group" "opa_asg" {
  name             = "${var.environment}-opa-asg"
  desired_capacity = 1
  min_size         = 1
  max_size         = 3

  vpc_zone_identifier = [data.aws_subnet.ecs_subnet_1b.id]

  health_check_type         = "EC2"
  health_check_grace_period = 60

  termination_policies  = ["Default"]
  default_cooldown      = 60
  protect_from_scale_in = true
  capacity_rebalance    = true

  enabled_metrics = [
    "GroupMinSize",
    "GroupMaxSize",
    "GroupDesiredCapacity",
    "GroupInServiceInstances",
    "GroupPendingInstances",
    "GroupStandbyInstances",
    "GroupTerminatingInstances",
    "GroupTotalInstances",
    "GroupInServiceCapacity",
    "GroupPendingCapacity",
    "GroupStandbyCapacity",
    "GroupTerminatingCapacity",
    "GroupTotalCapacity",
    "WarmPoolDesiredCapacity",
    "WarmPoolWarmedCapacity",
    "WarmPoolPendingCapacity",
    "WarmPoolTerminatingCapacity",
    "WarmPoolTotalCapacity",
    "GroupAndWarmPoolDesiredCapacity",
    "GroupAndWarmPoolTotalCapacity"
  ]

  mixed_instances_policy {
    instances_distribution {
      spot_allocation_strategy                 = "price-capacity-optimized"
      on_demand_percentage_above_base_capacity = 100
    }

    launch_template {
      launch_template_specification {
        launch_template_id = aws_launch_template.opa_lt_amd.id
        version            = "$Latest"
      }

      override {
        instance_type = "c7g.2xlarge"
        launch_template_specification {
          launch_template_id = aws_launch_template.opa_lt_arm.id
          version            = "$Latest"
        }
      }

      override {
        instance_type = "c7i.2xlarge"
      }
    }
  }

  tag {
    key                 = "Name"
    propagate_at_launch = true
    value               = "opa-ecs-node"
  }

  tag {
    key                 = "AmazonECSManaged"
    propagate_at_launch = true
    value               = ""
  }

  tag {
    key                 = "cost:namespace"
    propagate_at_launch = true
    value               = "data-products"
  }

  tag {
    key                 = "cost:application"
    propagate_at_launch = true
    value               = "opal_client"
  }

  tag {
    key                 = "cost:environment"
    propagate_at_launch = true
    value               = var.environment
  }

  tag {
    key                 = "cost:component"
    propagate_at_launch = true
    value               = "ec2"
  }

  tag {
    key                 = "iac:managed"
    propagate_at_launch = true
    value               = "true"
  }

  tag {
    key                 = "iac:name"
    propagate_at_launch = true
    value               = "terraform"
  }

  tag {
    key                 = "iac:code_repo"
    propagate_at_launch = true
    value               = "github.com/grofers/banalytics-infrastructure"
  }

  tag {
    key                 = "blinkit:resource_group"
    propagate_at_launch = true
    value               = "opa"
  }

  tag {
    key                 = "blinkit:terraform"
    propagate_at_launch = true
    value               = "true"
  }

  lifecycle {
    ignore_changes = [
      desired_capacity,
      max_size,
      min_size
    ]
  }
} 