resource "aws_ecs_cluster" "preprod_services_cluster_v2" {
  name = "${var.environment}-${var.service_name}"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  tags = merge(
    local.common_tags,
    {
      "cost:component" = "ecs"
    }
  )
}

module "preprod_services_cluster_v2" {
  source       = "../../../modules/ecs_cluster"
  cluster_name = aws_ecs_cluster.preprod_services_cluster_v2.name
  asg_name     = "${var.environment}-${var.service_name}"
  asg_max_size = 1

  #tags for ASG
  asg_key1         = "Type"
  asg_value1       = var.service_name
  cost_component   = "ASG"
  cost_application = "${var.environment}-${var.service_name}"

  spot            = false
  target_capacity = 100

  instance_types = {
    "m7i.2xlarge" = 1
  }

  lt_id = aws_launch_template.preprod_services_cluster_v2_lt.id

  subnets_ids = [data.aws_subnet.ecs_subnet_1b.id]

  cp_tags = {
    "cost:application"       = var.service_name
    "cost:component"         = "ecs"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  }
}

resource "aws_ecs_cluster_capacity_providers" "preprod_services_cluster_v2_cp" {
  cluster_name = aws_ecs_cluster.preprod_services_cluster_v2.name
  capacity_providers = [
    module.preprod_services_cluster_v2.cp_name,
    aws_ecs_capacity_provider.opal_cp.name
  ]

  default_capacity_provider_strategy {
    capacity_provider = module.preprod_services_cluster_v2.cp_name
    weight            = 1
    base              = 0
  }
} 