#---------------------
# VPC
#---------------------

data "aws_vpc" "analytics_vpc" {
  id = var.analytics_vpc
}

#---------------------
# Subnets
#---------------------

data "aws_subnet" "ecs_subnet_1b" {
  id = var.ecs_subnet_1b
}

#---------------------
# IAM
#---------------------

data "aws_iam_instance_profile" "ecs_instance_role" {
  name = "ecsInstanceRole"
}

#---------------------
# ALB
#---------------------

data "aws_lb" "internal_alb" {
  name = "preprod-trino-alb"
}

data "aws_lb_listener" "internal_http_listener" {
  load_balancer_arn = data.aws_lb.internal_alb.arn
  port              = 80
}

#---------------------
# AMI
#---------------------
data "aws_ssm_parameter" "ecs_ami_amd_param" {
  name = "/aws/service/ecs/optimized-ami/amazon-linux-2/recommended/image_id"
}

data "aws_ssm_parameter" "ecs_ami_arm_param" {
  name = "/aws/service/ecs/optimized-ami/amazon-linux-2/arm64/recommended/image_id"
}

data "aws_ami" "ecs_amd_ami" {
  most_recent = true
  filter {
    name   = "image-id"
    values = [data.aws_ssm_parameter.ecs_ami_amd_param.value]
  }
}

data "aws_ami" "ecs_arm_ami" {
  most_recent = true
  filter {
    name   = "image-id"
    values = [data.aws_ssm_parameter.ecs_ami_arm_param.value]
  }
} 