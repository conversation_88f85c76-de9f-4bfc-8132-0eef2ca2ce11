variable "service_name" {
  type        = string
  description = "Name of the service"
}

variable "environment" {
  type        = string
  default     = "preprod"
  description = "Environment name (prod, preprod, dev)"
}

variable "env" {
  type        = string
  default     = "preprod"
  description = "Short environment name"
}

variable "aws_provider_region" {
  type        = string
  description = "AWS region for the provider"
}

variable "aws_profile" {
  type        = string
  description = "AWS profile to use for authentication"
}

variable "ecs_subnet_1b" {
  type        = string
  description = "ECS subnet ID for availability zone 1b"
}

variable "analytics_vpc" {
  type        = string
  description = "VPC ID for analytics services"
}

variable "launch_template_config" {
  type = object({
    block_size_gb = number
    key_name      = string
  })
  description = "Configuration for EC2 launch template"
}

variable "ecs_config" {
  type = object({
    deployment_maximum_percent         = number
    deployment_minimum_healthy_percent = number
  })
  description = "ECS deployment configuration"
}

# OPA variables
variable "opal_client_ecr_repository" {
  type = object({
    image_path = string
    image_tag  = string
  })
  description = "OPAL client ECR repository configuration"
}

variable "otel_collector_ecr_repository" {
  type = object({
    image_path = string
    image_tag  = string
  })
  description = "OpenTelemetry collector ECR repository configuration"
}

variable "otel_collector_secrets" {
  type        = list(any)
  description = "List of secrets for OpenTelemetry collector"
}

variable "opal_secrets" {
  type        = list(any)
  description = "List of secrets for OPAL client"
}

variable "opal_client_execution_role_arn" {
  type        = string
  description = "ARN of IAM role for OPAL client task execution"
}

# OPAL Server variables
variable "opal_server_ecr_repository" {
  type = object({
    image_path = string
    image_tag  = string
  })
  description = "OPAL server ECR repository configuration"
}

variable "opal_server_secrets" {
  type        = list(any)
  description = "List of secrets for OPAL server"
}

variable "opal_server_execution_role_arn" {
  type        = string
  description = "ARN of IAM role for OPAL server task execution"
}

variable "datadog_secrets" {
  type        = list(any)
  description = "List of secrets for Datadog agent"
} 