resource "aws_ecs_capacity_provider" "opal_cp" {
  name = "${var.environment}-opa-cp"

  auto_scaling_group_provider {
    auto_scaling_group_arn         = aws_autoscaling_group.opa_asg.arn
    managed_termination_protection = "ENABLED"

    managed_scaling {
      status          = "ENABLED"
      target_capacity = 100
    }
  }
  tags = merge(
    local.common_tags,
    {
      "cost:application"      = "opal_client"
      "cost:component"        = "capacity-provider"
      "blinkit:resource_group" = "opa"
    }
  )
} 