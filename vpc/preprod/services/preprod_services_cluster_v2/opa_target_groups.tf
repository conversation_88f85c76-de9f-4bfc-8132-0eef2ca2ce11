resource "aws_lb_target_group" "opal_client_tg" {
  name                 = "${var.environment}-opal-client-tg"
  port                 = 8181
  protocol             = "HTTP"
  protocol_version     = "HTTP1"
  target_type          = "ip"
  vpc_id               = data.aws_vpc.analytics_vpc.id
  deregistration_delay = "30"

  health_check {
    path                = "/health"
    matcher             = "200,302"
    healthy_threshold   = 2
    unhealthy_threshold = 7
    timeout             = 30
    interval            = 60
  }

  tags = merge(
    local.common_tags,
    {
      "Name"           = "${var.environment}-opal-client-tg"
      "cost:component" = "target-group"
    }
  )
}

resource "aws_lb_listener_rule" "opal_client_listener_v2" {
  listener_arn = data.aws_lb_listener.internal_http_listener.arn
  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.opal_client_tg.arn
  }

  condition {
    host_header {
      values = ["opal-client.analytics.blinkit.dev"]
    }
  }
}

resource "aws_lb_target_group" "opal_server_tg" {
  name                 = "${var.environment}-opal-server-tg"
  port                 = 7002
  protocol             = "HTTP"
  protocol_version     = "HTTP1"
  target_type          = "ip"
  vpc_id               = data.aws_vpc.analytics_vpc.id
  deregistration_delay = "30"

  health_check {
    path                = "/healthcheck"
    matcher             = "200,302"
    healthy_threshold   = 2
    unhealthy_threshold = 7
    timeout             = 30
    interval            = 60
  }

  tags = merge(
    local.common_tags,
    {
      "Name"           = "${var.environment}-opal-server-tg"
      "cost:component" = "target-group"
    }
  )
}

resource "aws_lb_listener_rule" "opal_server_listener_v2" {
  listener_arn = data.aws_lb_listener.internal_http_listener.arn
  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.opal_server_tg.arn
  }

  condition {
    host_header {
      values = ["opal-server.analytics.blinkit.dev"]
    }
  }
} 