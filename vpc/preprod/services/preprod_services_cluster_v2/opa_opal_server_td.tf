resource "aws_ecs_task_definition" "opal-server" {
  family       = "preprod-opal-server"
  network_mode = "awsvpc"
  container_definitions = jsonencode([
    {
      name      = "opal_server"
      image     = "${var.opal_server_ecr_repository.image_path}:${var.opal_server_ecr_repository.image_tag}"
      cpu       = 0
      memory    = 512
      essential = true
      portMappings = [
        {
          name          = "opal-server-tcp-port"
          containerPort = 7002
          hostPort      = 7002
          protocol      = "tcp"
          appProtocol   = "http"
        },
        {
          name          = "statsd"
          containerPort = 8125
          hostPort      = 8125
          protocol      = "tcp"
          appProtocol   = "http"
        }
      ]
      secrets = "${var.opal_server_secrets}"
      environment = [
        {
          "name" : "OPAL_POLICY_REPO_MANIFEST_PATH",
          "value" : ".opal_manifest"
        },
        {
          "name" : "OPAL_ENABLE_DATADOG_APM",
          "value" : "true"
        },
        {
          "name" : "OPAL_POLICY_SOURCE_TYPE",
          "value" : "API"
        },
        {
          "name" : "OPAL_ENABLE_METRICS",
          "value" : "true"
        },
        {
          "name" : "DD_AGENT_HOST",
          "value" : "localhost"
        },
        {
          "name" : "DD_VERSION",
          "value" : "1.0.1"
        },
        {
          "name" : "OPAL_BROADCAST_URI",
          "value" : "redis://preprod-opa-redis.fciqdw.ng.0001.apse1.cache.amazonaws.com:6379/0"
        },
        {
          "name" : "DD_SERVICE",
          "value" : "opal"
        },
        {
          "name" : "DD_ENV",
          "value" : "preprod"
        },
        {
          "name" : "OPAL_STATISTICS_ENABLED",
          "value" : "false"
        },
        {
          "name" : "POLICY_BUNDLE_SERVER_AWS_REGION",
          "value" : "ap-southeast-1"
        }
      ]
      mountPoints = []
      volumesFrom = []
      dockerLabels = {
        "com.datadoghq.tags.service" : "opal",
        "com.datadoghq.tags.version" : "1.0.0",
        "com.datadoghq.tags.env" : "preprod"
      }
      logConfiguration = {
        "logDriver" = "awslogs",
        "options" = {
          "awslogs-group"         = "/ecs/opal-server"
          "mode"                  = "non-blocking"
          "awslogs-create-group"  = "true"
          "max-buffer-size"       = "25m"
          "awslogs-region"        = "ap-southeast-1"
          "awslogs-stream-prefix" = "ecs"
        }
      }
      systemControls = []
    },
    {
      name      = "datadog-agent"
      image     = "public.ecr.aws/datadog/agent:latest"
      cpu       = 10
      memory    = 512
      essential = true
      portMappings = [
        {
          containerPort = 8126
          hostPort      = 8126
          protocol      = "tcp"
        }
      ]
      environment    = []
      mountPoints    = []
      volumesFrom    = []
      secrets        = "${var.datadog_secrets}"
      systemControls = []
    }
  ])
  task_role_arn      = aws_iam_role.opal_server_task_execution_role.arn
  execution_role_arn = var.opal_server_execution_role_arn
  skip_destroy       = true
  requires_compatibilities = [
    "EC2"
  ]
  cpu    = "256"
  memory = "1024"
  tags = {
    "cost:application"       = "opal_server"
    "cost:component"         = "task_definition"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = "opa"
    "blinkit:terraform"      = "true"
    "tenant"                 = "blinkit"
  }
} 