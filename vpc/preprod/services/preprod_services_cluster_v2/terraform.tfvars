service_name        = "services-cluster-v2"
environment         = "preprod"
env                 = "preprod"
aws_provider_region = "ap-southeast-1"
aws_profile         = "blinkit_analytics"

ecs_subnet_1b = "subnet-088a1566c712c2761"

analytics_vpc = "vpc-0a1245931c5ad6a4c"

launch_template_config = {
  block_size_gb = 128
  key_name      = "prod-trino"
}

ecs_config = {
  deployment_maximum_percent         = 100
  deployment_minimum_healthy_percent = 0
}

opal_client_ecr_repository = {
  image_path = "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/opal/opal-client"
  image_tag  = "v0.7.16_1613d8359f45"
}

otel_collector_ecr_repository = {
  image_path = "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/otel-collector"
  image_tag  = "v0.122.0_41edadccc3d5"
}

opal_secrets = [
  {
    "name" : "OPAL_SERVER_URL",
    "valueFrom" : "arn:aws:ssm:ap-southeast-1:183295456051:parameter/secrets/preprod/opa/server-data-uri"
  },
  {
    "name" : "OPAL_DEFAULT_UPDATE_CALLBACKS",
    "valueFrom" : "arn:aws:ssm:ap-southeast-1:183295456051:parameter/secrets/preprod/opa/update_callback_uri"
  }
]

otel_collector_secrets = [
  {
    "name" : "KAFKA_DEFAULT_BROKER",
    "valueFrom" : "arn:aws:ssm:ap-southeast-1:125719378300:parameter/secrets/preprod/warpstream/vcn_dataplatform_preprod_cdc_cluster/discovery"
  }
]

opal_client_execution_role_arn = "arn:aws:iam::183295456051:role/ecsTaskExecutionRole"

opal_server_ecr_repository = {
  image_path = "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/opal/opal-server"
  image_tag  = "v1.0.0_83cd78141076"
}

opal_server_secrets = [
  {
    "name" : "OPAL_POLICY_BUNDLE_URL",
    "valueFrom" : "/secrets/preprod/opa/policy_bundle_url"
  },
  {
    "name" : "OPAL_DATA_CONFIG_SOURCES",
    "valueFrom" : "/secrets/preprod/opa/data_config_sources"
  }
]

opal_server_execution_role_arn = "arn:aws:iam::183295456051:role/ecsTaskExecutionRole"

datadog_secrets = [
  {
    "name" : "DD_API_KEY",
    "valueFrom" : "/dataplatform/preprod/infra/datadog-agent/api-key"
  }
] 