resource "aws_elasticache_replication_group" "opa_redis_replication_group" {
  replication_group_id = "${var.environment}-${var.service_name}"
  description          = "replication group for ${var.environment}-${var.service_name}"

  engine               = "redis"
  engine_version       = "7.1"
  node_type            = var.redis_cluster_config.node_type
  parameter_group_name = "default.redis7"

  automatic_failover_enabled = true
  auto_minor_version_upgrade = true

  replicas_per_node_group = var.redis_cluster_config.replicas
  num_node_groups         = var.redis_cluster_config.shards

  subnet_group_name = aws_elasticache_subnet_group.opa_redis_subnet_group.name
  security_group_ids = [
    aws_security_group.opa_redis_cache_sg.id
  ]

  snapshot_retention_limit = 15
  snapshot_window          = "21:00-22:00"
  maintenance_window       = "thu:22:30-thu:23:30"

  tags = {
    "cost:application"       = var.service_name
    "cost:component"         = "elasticache"
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
    "cost:environment"       = var.environment
  }

  lifecycle {
    ignore_changes = [
      engine_version,
      num_node_groups,
      replicas_per_node_group,
      multi_az_enabled,
      parameter_group_name
    ]
  }
} 