resource "aws_security_group" "opa_redis_cache_sg" {
  name        = "${var.environment}-${var.service_name}-cache-sg"
  description = "Security group for ${var.environment}-${var.service_name} cache"
  vpc_id      = data.aws_vpc.analytics_vpc.id

  ingress {
    description     = "Redis port from security group preprod_opal_client_sg"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [data.aws_security_group.preprod_opal_client_sg.id]
  }

  ingress {
    description     = "Redis port from security group preprod_opal_server_sg"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [data.aws_security_group.preprod_opal_server_sg.id]
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name                     = "${var.environment}-${var.service_name}-cache-sg"
    "cost:application"       = var.service_name
    "cost:component"         = "security-group"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  }
} 