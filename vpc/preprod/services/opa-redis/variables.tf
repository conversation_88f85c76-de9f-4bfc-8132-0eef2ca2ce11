variable "service_name" {
  type        = string
  description = "Name of the service"
  default     = "opa-redis"
}

variable "environment" {
  type        = string
  description = "Environment name"
  default     = "preprod"
}

variable "redis_cluster_config" {
  type = object({
    node_type = string
    shards    = number
    replicas  = number
  })
  description = "Redis cluster configuration"
  default = {
    node_type = "cache.t4g.medium"
    shards    = 1
    replicas  = 1
  }
} 