environment           = "preprod"
service_name          = "trino-gateway"
aws_provider_region   = "ap-southeast-1"
vpc_id                = "vpc-0a1245931c5ad6a4c"
availability_zones    = ["ap-southeast-1a", "ap-southeast-1b"]
instance_size         = "db.r5.large"  
component             = "aurora"
subnet_id_1a          = "subnet-0f1b127fddfca6e13"
subnet_id_1b          = "subnet-088a1566c712c2761"
subnet_id_1c          = "subnet-03e86d09ca089a9b2"
aws_profile           = "blinkit_analytics"

aurora_cluster_config = {
  master_username = "admin"
  engine          = "aurora-mysql"
  engine_mode     = "provisioned"
  engine_version  = "8.0.mysql_aurora.3.04.0"
  engine_family   = "aurora-mysql8.0"
}

# EC2 Configuration
ami_id                 = "ami-016fc512f18751c3f"
ec2_instance_type      = "c5.xlarge"
ec2_security_group_ids = ["sg-08ae757fdef824220"]
key_name              = "prod-trino"

# Security Group Configuration
preprod_vpc_cidr = "10.26.0.0/16"
mysql_port           = 3306 