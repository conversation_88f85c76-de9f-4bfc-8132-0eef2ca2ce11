variable "service_name" {
  type = string
}

variable "environment" {
  type    = string
  default = "preprod"
}

variable "aws_provider_region" {
  type = string
}

variable "vpc_id" {
  type = string
}

variable "availability_zones" {
  type = list(string)
}

variable "instance_size" {
  type = string
}

variable "component" {
  type = string
}

variable "subnet_id_1b" {
  type = string
}

variable "subnet_id_1a" {
  type = string
}

variable "subnet_id_1c" {
  type = string
}

variable "aws_profile" {
  type = string
}

variable "aurora_cluster_config" {
  type = object({
    master_username = string
    engine          = string
    engine_mode     = string
    engine_version  = string
    engine_family   = string
  })
}

# EC2 Configuration Variables
variable "ami_id" {
  description = "AMI ID for the EC2 instance"
  type        = string
}

variable "ec2_instance_type" {
  description = "EC2 instance type"
  type        = string
}

variable "ec2_security_group_ids" {
  description = "List of security group IDs for EC2"
  type        = list(string)
}

variable "key_name" {
  description = "Key pair name for EC2 access"
  type        = string
}

variable "preprod_vpc_cidr" {
  description = "CIDR block for internal network access"
  type        = string
}

variable "mysql_port" {
  description = "MySQL port number"
  type        = number
  default     = 3306
} 