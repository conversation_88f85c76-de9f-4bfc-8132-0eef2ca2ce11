resource "aws_iam_role" "trino_gateway" {
  name = "${var.environment}-${var.service_name}-role"
  
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
  
  tags = {
    "cost:application"       = var.service_name
    "cost:component"         = "iam-role"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
}

resource "aws_iam_instance_profile" "trino_gateway" {
  name = "${var.environment}-${var.service_name}-role"
  role = aws_iam_role.trino_gateway.name
  
  tags = {
    "cost:application"       = var.service_name
    "cost:component"         = "iam-instance-profile"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
} 