resource "aws_lb_target_group" "trino_gateway" {
  name        = "${var.environment}-${var.service_name}-tg"
  port        = 8889
  protocol    = "HTTP"
  vpc_id      = var.vpc_id
  target_type = "instance"

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }

  tags = {
    Name                     = "${var.environment}-${var.service_name}-tg"
    "cost:application"       = var.service_name
    "cost:component"         = "target-group"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
}

resource "aws_lb_target_group_attachment" "trino_gateway" {
  target_group_arn = aws_lb_target_group.trino_gateway.arn
  target_id        = aws_instance.trino_gateway.id
  port             = 8889
}

resource "aws_lb_listener_rule" "trino_gateway" {
  listener_arn = data.aws_lb_listener.trino_https_listener.arn
  priority     = 100

  action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.trino_gateway.arn
  }

  condition {
    host_header {
      values = ["preprod-trino.analytics.blinkit.dev"]
    }
  }

  tags = {
    Name                     = "${var.environment}-${var.service_name}-listener-rule"
    "cost:application"       = var.service_name
    "cost:component"         = "listener-rule"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
} 