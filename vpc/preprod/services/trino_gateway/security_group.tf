resource "aws_security_group" "cluster_sg" {
  name        = "${var.environment}-${var.service_name}-sg"
  description = "Security group for ${var.environment}-${var.service_name} cluster"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = var.mysql_port  # MySQL port
    to_port     = var.mysql_port
    protocol    = "tcp"
    cidr_blocks = [var.preprod_vpc_cidr]  # Internal network access
    description = "MySQL access from internal network"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "All outbound traffic"
  }

  tags = {
    Name                     = "${var.environment}-${var.service_name}-sg"
    "cost:application"       = var.service_name
    "cost:component"         = "security-group"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
    "temp:testing"           = true
    "karpenter.sh/discovery" = "eks-preprod-apse1-analytics"
  }
} 