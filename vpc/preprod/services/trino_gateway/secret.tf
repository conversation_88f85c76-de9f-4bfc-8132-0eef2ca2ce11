resource "aws_ssm_parameter" "db_password" {
  name  = "/secrets/${var.environment}/${var.service_name}/db_password"
  type  = "SecureString"
  value = random_password.db_password.result

  tags = {
    "cost:application"       = var.service_name
    "cost:component"         = "secret"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
} 