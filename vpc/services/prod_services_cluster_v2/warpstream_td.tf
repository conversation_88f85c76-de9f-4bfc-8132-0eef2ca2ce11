resource "aws_ecs_task_definition" "warpstream" {
  family       = "prod-warpstream-cdc-cluster"
  network_mode = "awsvpc"
  container_definitions = jsonencode([
    {
      name      = "warpstream_agent"
      image     = "public.ecr.aws/warpstream-labs/warpstream_agent:v620"
      cpu       = 0
      memory    = 7168
      essential = true
      portMappings = [
        {
          name          = "warpstream_agent-9092-tcp"
          containerPort = 9092
          hostPort      = 9092
          protocol      = "tcp"
          appProtocol   = "http"
        },
        {
          name          = "warpstream_agent-8080-tcp"
          containerPort = 8080
          hostPort      = 8080
          protocol      = "tcp"
          appProtocol   = "http"
        }
      ]
      environment = [
        {
          "name" : "WARPSTREAM_BUCKET_URL",
          "value" : "s3://prod-warpstream-cdc-cluster?region=ap-southeast-1"
        },
        {
          "name" : "WARPSTREAM_AGENT_GROUP",
          "value" : "blinkit-analytics-agent"
        },
        {
          "name" : "GOMAXPROCS",
          "value" : "2"
        },
        {
          "name" : "WARPSTREAM_REGION",
          "value" : "ap-southeast-1"
        },
        {
          "name" : "WARPSTREAM_DEFAULT_VIRTUAL_CLUSTER_ID",
          "value" : "vci_7c2389f8_506e_445a_898f_15e2060d8e8c"
        },
        {
          "name" : "WARPSTREAM_KAFKA_HIGH_CARDINALITY_METRICS",
          "value" : "true"
        }
      ]
      mountPoints = []
      volumesFrom = []
      dockerLabels = {
        "PROMETHEUS_EXPORTER_PORT" : "8080",
        "environment" : "prod",
        "PROMETHEUS_EXPORTER_JOB_NAME" : "warpstream-agents",
        "PROMETHEUS_EXPORTER_PATH" : "/metrics",
        "type" : "warpstream-agents",
        "service_name" : "warpstream-cdc-cluster"
      }
      logConfiguration = {
        "logDriver" = "json-file",
        "options" = {
          "max-size" = "50m",
          "max-file" = "2"
        }
      }
      systemControls = []
    }
  ])
  task_role_arn      = aws_iam_role.warpstream_task_execution_role.arn
  execution_role_arn = var.warpstream_execution_role_arn
  skip_destroy       = true
  requires_compatibilities = [
    "EC2"
  ]
  cpu    = "2048"
  memory = "7168"
  tags = {
    "cost:application"      = "warpstream"
    "cost:component"        = "task_definition"
    "cost:environment"      = var.environment
    "blinkit:resource_group" = "warpstream"
    "blinkit:terraform"      = "true"
    "tenant"                = "blinkit"
  }
} 