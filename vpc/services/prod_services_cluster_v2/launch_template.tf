resource "aws_launch_template" "prod_services_cluster_v2_lt" {
  name                   = "${var.environment}-${var.service_name}-lt"
  image_id               = data.aws_ami.ecs_amd_ami.id
  vpc_security_group_ids = [aws_security_group.prod_services_cluster_v2_sg.id]
  key_name               = var.launch_template_config.key_name
  user_data              = base64encode(data.template_file.prod_services_cluster_v2_lt_user_data.rendered)

  iam_instance_profile {
    arn = data.aws_iam_instance_profile.ecs_instance_role.arn
  }

  tag_specifications {
    resource_type = "volume"
    tags = merge(
      local.common_tags,
      {
        "cost:component" = "ebs"
      }
    )
  }

  tag_specifications {
    resource_type = "instance"
    tags = merge(
      local.common_tags,
      {
        "cost:component" = "ec2"
      }
    )
  }

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      volume_size           = var.launch_template_config.block_size_gb
      volume_type           = "gp3"
      delete_on_termination = true
      encrypted             = true
      snapshot_id           = data.aws_ami.ecs_amd_ami.root_snapshot_id
    }
  }

  disable_api_termination = false

  monitoring {
    enabled = true
  }

  tags = merge(
    local.common_tags,
    {
      "cost:component" = "launch-template"
    }
  )
}