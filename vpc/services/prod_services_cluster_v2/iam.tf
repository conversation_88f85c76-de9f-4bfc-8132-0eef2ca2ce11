resource "aws_iam_role" "opal_client_task_execution_role" {
  name = "blinkit-${var.environment}-opal-client-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
      {
        Sid = ""
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = merge(
    local.common_tags,
    {
      "cost:application"      = "opal_client"
      "cost:component"        = "iam-role"
      "blinkit:resource_group" = "opa"
    }
  )
}

resource "aws_iam_role" "opal_server_task_execution_role" {
  name = "blinkit-${var.environment}-opal-server-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
      {
        Sid = ""
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = merge(
    local.common_tags,
    {
      "cost:application"      = "opal_server"
      "cost:component"        = "iam-role"
      "blinkit:resource_group" = "opa"
    }
  )
}

resource "aws_iam_policy" "opal_common_ecs_policy" {
  name        = "blinkit-${var.environment}-opal-common-ecs-policy"
  description = "Common policy for OPAL services to access ECS and EC2 resources"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "VisualEditor0"
        Effect = "Allow"
        Action = [
          "ecs:ListServices",
          "ec2:DescribeInstances",
          "ec2:DescribeTags",
          "ecs:ListTasks",
          "ecs:DescribeServices",
          "ecs:DescribeContainerInstances",
          "ecs:DescribeTasks",
          "ecs:DescribeTaskDefinition"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(
    local.common_tags,
    {
      "cost:application"      = "opal_common"
      "cost:component"        = "iam-policy"
      "blinkit:resource_group" = "opa"
    }
  )
}

resource "aws_iam_policy" "opal_common_policy" {
  name        = "blinkit-${var.environment}-opal-common-policy"
  description = "Common policy for OPAL services to access SSM parameters and S3 resources"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "SSMAccess"
        Effect = "Allow"
        Action = [
          "ssm:GetParameterHistory",
          "ssm:GetParametersByPath",
          "ssm:GetParameters",
          "ssm:GetParameter"
        ]
        Resource = [
          "arn:aws:ssm:ap-southeast-1:183295456051:parameter/secrets/prod/opa/data_config_sources",
          "arn:aws:ssm:ap-southeast-1:183295456051:parameter/secrets/prod/opa/policy_bundle_url",
          "arn:aws:ssm:ap-southeast-1:183295456051:parameter/secrets/prod/opa/server-data-uri",
          "arn:aws:ssm:ap-southeast-1:183295456051:parameter/secrets/prod/opa/update_callback_uri"
        ]
      },
      {
        Sid = "S3Access"
        Effect = "Allow"
        Action = [
          "s3:Get*",
          "s3:List*"
        ]
        Resource = [
          "arn:aws:s3:::data-access-policies",
          "arn:aws:s3:::data-access-policies/*",
          "arn:aws:s3:::data-access-policies.s3.ap-southeast-1.amazonaws.com",
          "arn:aws:s3:::data-access-policies.s3.ap-southeast-1.amazonaws.com/*"
        ]
      }
    ]
  })

  tags = merge(
    local.common_tags,
    {
      "cost:application"      = "opal_common"
      "cost:component"        = "iam-policy"
      "blinkit:resource_group" = "opa"
    }
  )
}

resource "aws_iam_role_policy_attachment" "opal_server_ecs_policy_attachment" {
  role       = aws_iam_role.opal_server_task_execution_role.name
  policy_arn = aws_iam_policy.opal_common_ecs_policy.arn
}

resource "aws_iam_role_policy_attachment" "opal_server_common_policy_attachment" {
  role       = aws_iam_role.opal_server_task_execution_role.name
  policy_arn = aws_iam_policy.opal_common_policy.arn
}

resource "aws_iam_role_policy_attachment" "opal_client_common_policy_attachment" {
  role       = aws_iam_role.opal_client_task_execution_role.name
  policy_arn = aws_iam_policy.opal_common_policy.arn
}

resource "aws_iam_role_policy_attachment" "opal_client_ecs_policy_attachment" {
  role       = aws_iam_role.opal_client_task_execution_role.name
  policy_arn = aws_iam_policy.opal_common_ecs_policy.arn
}

resource "aws_iam_role" "warpstream_task_execution_role" {
  name = "blinkit-${var.environment}-warpstream-task-execution-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
      {
        Sid = ""
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })

  tags = merge(
    local.common_tags,
    {
      "cost:application"      = "warpstream"
      "cost:component"        = "iam-role"
      "blinkit:resource_group" = "warpstream"
    }
  )
}

resource "aws_iam_policy" "warpstream_s3_policy" {
  name        = "blinkit-${var.environment}-warpstream-s3-policy"
  description = "Policy for warpstream service to access S3 resources"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "S3Access"
        Effect = "Allow"
        Action = [
          "s3:Get*",
          "s3:List*",
          "s3:Put*",
          "s3:Delete*"
        ]
        Resource = [
          "arn:aws:s3:::prod-warpstream-cdc-cluster",
          "arn:aws:s3:::prod-warpstream-cdc-cluster/*"
        ]
      }
    ]
  })

  tags = merge(
    local.common_tags,
    {
      "cost:application"      = "warpstream"
      "cost:component"        = "iam-policy"
      "blinkit:resource_group" = "warpstream"
    }
  )
}

resource "aws_iam_role_policy_attachment" "warpstream_s3_policy_attachment" {
  role       = aws_iam_role.warpstream_task_execution_role.name
  policy_arn = aws_iam_policy.warpstream_s3_policy.arn
} 