resource "aws_security_group" "prod_services_cluster_v2_sg" {
  name        = "prod-services-cluster-v2-sg"
  description = "Security group for production services cluster v2"
  vpc_id      = data.aws_vpc.analytics_vpc.id

  ingress {
    description = "SSH access from specific IP"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Allow all traffic from complete VPC"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["*********/16"]
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    local.common_tags,
    {
      "Name"                   = "prod-services-cluster-v2-sg"
      "cost:component"         = "security-group"
    }
  )
}

resource "aws_security_group" "opal_client_sg" {
  name        = "opal-client-sg"
  description = "Security group for OPAL client service"
  vpc_id      = data.aws_vpc.analytics_vpc.id

  ingress {
    description = "SSH access from specific IP"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Access from Trino SG"
    from_port   = 8181
    to_port     = 8181
    protocol    = "tcp"
    security_groups = ["sg-0fa1113efaa345a7d"]
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    local.common_tags,
    {
      "Name"                   = "opal-client-sg"
      "cost:component"         = "security-group"
    }
  )
}

resource "aws_security_group" "opal_server_sg" {
  name        = "opal-server-sg"
  description = "Allow Datasync between client and server, and data updates to server"
  vpc_id      = data.aws_vpc.analytics_vpc.id

  ingress {
    description     = "Allow Ingress presto sg"
    from_port       = 7002
    to_port         = 8126
    protocol        = "tcp"
    security_groups = ["sg-0fa1113efaa345a7d"]
  }

  ingress {
    description = "Allow ingress from opal client"
    from_port   = 7002
    to_port     = 8126
    protocol    = "tcp"
    security_groups = [aws_security_group.opal_client_sg.id]
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    local.common_tags,
    {
      "Name"                   = "opal-server-sg"
      "cost:component"         = "security-group"
    }
  )
}

resource "aws_security_group_rule" "opal_client_7766_from_server" {
  type                     = "ingress"
  from_port               = 7766
  to_port                 = 7766
  protocol                = "tcp"
  source_security_group_id = aws_security_group.opal_server_sg.id
  security_group_id       = aws_security_group.opal_client_sg.id
  description             = "access to opal client api"
}

resource "aws_security_group" "warpstream_sg" {
  name        = "warpstream-sg"
  description = "Security group for warpstream service"
  vpc_id      = data.aws_vpc.analytics_vpc.id

  ingress {
    description = "SSH access from specific IP"
    from_port   = 9092
    to_port     = 9092
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Warpstream agent port 9092"
    from_port   = 9092
    to_port     = 9092
    protocol    = "tcp"
    cidr_blocks = ["*********/16"]
  }

  ingress {
    description = "Warpstream metrics port 8080"
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = ["*********/16"]
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(
    local.common_tags,
    {
      "Name"                   = "warpstream-sg"
      "cost:component"         = "security-group"
    }
  )
}


