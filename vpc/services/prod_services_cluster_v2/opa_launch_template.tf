resource "aws_launch_template" "opa_lt_arm" {
  name                   = "${var.environment}-opa-arm-lt"
  image_id               = data.aws_ami.ecs_arm_ami.id
  vpc_security_group_ids = [aws_security_group.opal_client_sg.id]
  key_name               = var.launch_template_config.key_name
  user_data              = base64encode(data.template_file.prod_services_cluster_v2_lt_user_data.rendered)


  iam_instance_profile {
    arn = data.aws_iam_instance_profile.ecs_instance_role.arn
  }

  tag_specifications {
    resource_type = "volume"
    tags = merge(
      local.common_tags,
      {
        "cost:application" = "opal_client"
        "cost:component"   = "ebs"
      }
    )
  }

  tag_specifications {
    resource_type = "instance"
    tags = merge(
      local.common_tags,
      {
        "cost:application"      = "opal_client"
        "cost:component"        = "ec2"
        "blinkit:resource_group" = "opa"
      }
    )
  }



  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      volume_size           = var.launch_template_config.block_size_gb
      volume_type           = "gp3"
      delete_on_termination = true
      encrypted             = true
      snapshot_id           = data.aws_ami.ecs_arm_ami.root_snapshot_id
    }
  }


  disable_api_termination = false

  monitoring {
    enabled = true
  }

  tags = merge(
    local.common_tags,
    {
      "cost:application"      = "opal_client"
      "cost:component"        = "launch-template"
      "blinkit:resource_group" = "opa"
    }
  )
}

resource "aws_launch_template" "opa_lt_amd" {
  name                   = "${var.environment}-opa-amd-lt"
  image_id               = data.aws_ami.ecs_amd_ami.id
  vpc_security_group_ids = [aws_security_group.opal_client_sg.id]
  key_name               = var.launch_template_config.key_name
  user_data              = base64encode(data.template_file.prod_services_cluster_v2_lt_user_data.rendered)

  iam_instance_profile {
    arn = data.aws_iam_instance_profile.ecs_instance_role.arn
  }

  tag_specifications {
    resource_type = "volume"
    tags = merge(
      local.common_tags,
      {
        "cost:application" = "opal_client"
        "cost:component"   = "ebs"
      }
    )
  }

  tag_specifications {
    resource_type = "instance"
    tags = merge(
      local.common_tags,
      {
        "cost:application"      = "opal_client"
        "cost:component"        = "ec2"
        "blinkit:resource_group" = "opa"
      }
    )
  }

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      volume_size           = var.launch_template_config.block_size_gb
      volume_type           = "gp3"
      delete_on_termination = true
      encrypted             = true
      snapshot_id           = data.aws_ami.ecs_amd_ami.root_snapshot_id
    }
  }

  disable_api_termination = false

  monitoring {
    enabled = true
  }

  tags = merge(
    local.common_tags,
    {
      "cost:application"      = "opal_client"
      "cost:component"        = "launch-template"
      "blinkit:resource_group" = "opa"
    }
  )
}