resource "aws_ecs_service" "opal_client" {
  name                               = "opal-client-service"
  cluster                            = aws_ecs_cluster.prod_services_cluster_v2.id
  task_definition                    = aws_ecs_task_definition.opal-client.family
  desired_count                      = 0
  scheduling_strategy                = "REPLICA"
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100

  deployment_controller {
    type = "ECS"
  }

  network_configuration {
    subnets         = [data.aws_subnet.ecs_subnet_1b.id]
    security_groups = [aws_security_group.opal_client_sg.id]
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.opal_client_tg.arn
    container_name   = "opal_client"
    container_port   = 8181
  }


  enable_ecs_managed_tags = true

  capacity_provider_strategy {
    base              = 0
    capacity_provider = aws_ecs_capacity_provider.opal_cp.name
    weight            = 1
  }

  ordered_placement_strategy {
    type  = "spread"
    field = "attribute:ecs.availability-zone"
  }

  ordered_placement_strategy {
    type  = "spread"
    field = "instanceId"
  }

  ordered_placement_strategy {
    type  = "binpack"
    field = "memory"
  }


  tags = merge(
    local.common_tags,
    {
      "cost:component" = "ecs-service"
    }
  )

  lifecycle {
    ignore_changes = [
      desired_count,
      task_definition,
      launch_type,
    ]
  }
}

resource "aws_ecs_service" "opal_server" {
  name                               = "opal-server-service"
  cluster                            = aws_ecs_cluster.prod_services_cluster_v2.id
  task_definition                    = aws_ecs_task_definition.opal-server.family
  desired_count                      = 0
  scheduling_strategy                = "REPLICA"
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100

  deployment_controller {
    type = "ECS"
  }

  network_configuration {
    subnets         = [data.aws_subnet.ecs_subnet_1b.id]
    security_groups = [aws_security_group.opal_server_sg.id]
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.opal_server_tg.arn
    container_name   = "opal_server"
    container_port   = 7002
  }

  enable_ecs_managed_tags = true

  capacity_provider_strategy {
    base              = 0
    capacity_provider = aws_ecs_capacity_provider.opal_cp.name
    weight            = 1
  }

  ordered_placement_strategy {
    type  = "spread"
    field = "attribute:ecs.availability-zone"
  }

  ordered_placement_strategy {
    type  = "spread"
    field = "instanceId"
  }

  ordered_placement_strategy {
    type  = "binpack"
    field = "memory"
  }

  tags = merge(
    local.common_tags,
    {
      "cost:component" = "ecs-service"
    }
  )

  lifecycle {
    ignore_changes = [
      desired_count,
      task_definition,
      launch_type,
    ]
  }
}

