resource "aws_ecs_task_definition" "opal-client" {
  family       = "opal-client"
  network_mode = "awsvpc"
  container_definitions = jsonencode([
    {
      name      = "opal_client"
      image     = "${var.opal_client_ecr_repository.image_path}:${var.opal_client_ecr_repository.image_tag}"
      cpu       = 0
      memory    = 4096
      essential = true
      portMappings = [
        {
          name : "opa-tcp-port",
          containerPort : 8181,
          hostPort : 8181,
          protocol : "tcp",
          appProtocol : "http"
        },
        {
          name : "opal-client-tcp-port",
          containerPort : 7766,
          hostPort : 7766,
          protocol : "tcp",
          appProtocol : "http"
        }
      ]
      secrets = "${var.opal_secrets}"
      environment = [
        {
          "name" : "OPAL_SHOULD_REPORT_ON_DATA_UPDATES",
          "value" : "true"
        },
        {
          "name" : "ETERNAL_TRINO_CLUSTER_NAME",
          "value" : "common-cluster"
        },
        {
          "name" : "OPAL_OPA_HEALTH_CHECK_POLICY_ENABLED",
          "value" : "true"
        },
        {
          "name" : "OPAL_FETCH_PROVIDER_MODULES",
          "value" : "opal_common.fetcher.providers,opal_fetcher_mysql.provider"
        },
        {
          "name" : "OPAL_DATA_TOPICS",
          "value" : "user_attributes,group_mappings,policy_data"
        },
        {
          "name" : "OPAL_INLINE_OPA_LOG_FORMAT",
          "value" : "full"
        },
        {
          "name" : "OPAL_LOG_SERIALIZE",
          "value" : "true"
        },
        {
          "name" : "OPAL_LOG_FORMAT",
          "value" : ""
        },
        {
          "name" : "GOMAXPROCS",
          "value" : "4"
        },
        {
          "name" : "OPAL_FETCHING_CALLBACK_TIMEOUT",
          "value" : "60"
        },
        {
          "name" : "GLOBAL_OTEL_COLLECTOR_URL",
          "value" : "http://localhost:8080/v1/generic"
        },
        {
          "name" : "OPAL_LOG_FORMAT_INCLUDE_PID",
          "value" : "true"
        },
        {
          "name" : "OPAL_LOG_LEVEL",
          "value" : "INFO"
        },
        {
          "name" : "OPAL_INLINE_OPA_CONFIG",
          "value" : "{\"config_file\": \"/app/config.yaml\"}"
        }
      ]
      mountPoints = []
      volumesFrom = []
      dockerLabels = {
        "PROMETHEUS_EXPORTER_PORT" : "8181",
        "service-name" : "opal-client",
        "environment" : "prod",
        "PROMETHEUS_EXPORTER_PATH" : "/metrics",
        "service" : "prod-opal-client",
        "PROMETHEUS_EXPORTER_JOB_NAME" : "prod-opal-client",
        "tenant" : "blinkit"
      }
      ulimits = [
        {
          "name" : "nofile",
          "softLimit" : 1048576,
          "hardLimit" : 1048576
        },
        {
          "name" : "nproc",
          "softLimit" : 1048576,
          "hardLimit" : 1048576
        }
      ]
      logConfiguration = {
        "logDriver" = "json-file",
        "options" = {
          "max-size" = "50m",
          "max-file" = "2"
        }
      }
      healthCheck = {
        "command" = [
          "CMD",
          "curl",
          "--fail",
          "http://localhost:8181/v1/data/system/opal/healthy"
        ]
        "interval"    = 10
        "timeout"     = 10
        "retries"     = 10
        "startPeriod" = 30
      }
      systemControls = []
    },
    {
      name      = "otel_collector"
      image     = "${var.otel_collector_ecr_repository.image_path}:${var.otel_collector_ecr_repository.image_tag}"
      cpu       = 0
      memory    = 512
      essential = true
      portMappings = [
        {
          name          = "otel-webhook-generic"
          containerPort = 8080
          hostPort      = 8080
          protocol      = "tcp"
          appProtocol   = "http"
        }
      ]
      environment = [
        {
          "name" : "KAFKA_DEFAULT_TOPIC",
          "value" : "eternal.otel.otel_logs"
        },
        {
          "name" : "KAFKA_OPA_LOGS_TOPIC",
          "value" : "eternal.audit_tools.opa.decision_logs"
        }
      ]
      mountPoints = []
      volumesFrom = []
      # secrets     = "${var.otel_collector_secrets}"
      logConfiguration = {
        "logDriver" = "json-file",
        "options" = {
          "max-size" = "50m",
          "max-file" = "2"
        }
      }
      systemControls = []
    }
  ])
  task_role_arn      = aws_iam_role.opal_client_task_execution_role.arn
  execution_role_arn = var.opal_client_execution_role_arn
  skip_destroy       = true
  requires_compatibilities = [
    "EC2"
  ]
  tags = {
    "cost:application"      = "opal_client"
    "cost:component"        = "task_definition"
    "cost:environment"      = var.environment
    "blinkit:resource_group" = "opa"
    "blinkit:terraform"      = "true"
    "tenant"                = "blinkit"
  }
}