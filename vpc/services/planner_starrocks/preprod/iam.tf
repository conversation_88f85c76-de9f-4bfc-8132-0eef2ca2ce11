resource "aws_iam_role" "serviceaccount-role" {
  name = "${var.environment}-blinkit-${var.iam_prefix}-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "defaultec2"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      },
      {
        Sid    = "eks"
        Effect = "Allow"
        Principal = {
          Federated = "arn:aws:iam::************:oidc-provider/oidc.eks.ap-southeast-1.amazonaws.com/id/C0C7EFCE1387FA36CE090693EEB90BAB"
        }
        Action = "sts:AssumeRoleWithWebIdentity"
        Condition = {
          "StringEquals" = {
            "oidc.eks.ap-southeast-1.amazonaws.com/id/C0C7EFCE1387FA36CE090693EEB90BAB:aud" = "sts.amazonaws.com"
          }
          "ForAnyValue:StringEquals" = {
            "oidc.eks.ap-southeast-1.amazonaws.com/id/C0C7EFCE1387FA36CE090693EEB90BAB:sub" = "system:serviceaccount:preprod-planner-starrocks:preprod-planner-starrocks-sa"
          }
        }
      },
      {
        Sid    = "selfAssume"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::************:role/preprod-blinkit-planner-starrocks-role"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
  tags = merge(local.common_tags,
    { "Name" : "${var.environment}-blinkit-${var.iam_prefix}-role" }
  )
}

resource "aws_iam_policy" "role-policy" {
  name = "${var.environment}-blinkit-${var.iam_prefix}-s3-access-policy"
  path = "/"

  tags = merge(local.common_tags,
    { "Name" : "${var.environment}-blinkit-${var.iam_prefix}-s3-access-policy" }
  )

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "blinkitS3Read"
        Effect = "Allow"
        Action = [
          "s3:Get*",
          "s3:List*"
        ]
        Resource = [
          "arn:aws:s3:::prod-data-lake-hudi-applications",
          "arn:aws:s3:::prod-data-lake-hudi-applications/*",
          "arn:aws:s3:::prod-data-lake-hudi-segment-events",
          "arn:aws:s3:::prod-data-lake-hudi-segment-events/*",
          "arn:aws:s3:::prod-data-lake-hudi-rudder-events",
          "arn:aws:s3:::prod-data-lake-hudi-rudder-events/*",
          "arn:aws:s3:::prod-data-lake-hudi-branch-events",
          "arn:aws:s3:::prod-data-lake-hudi-branch-events/*"
        ]
      },
      {
        Sid    = "zanalyticsS3Read"
        Effect = "Allow"
        Action = [
          "s3:List*",
          "s3:Get*"
        ]
        Resource = [
          "arn:aws:s3:::blinkit-derived-etls",
          "arn:aws:s3:::blinkit-derived-etls/*"
        ]
      },
      {
        Sid    = "zanalyticsS3Write"
        Effect = "Allow"
        Action = [
          "s3:DeleteObjectVersion",
          "s3:GetObjectVersionTagging",
          "s3:PutObjectVersionTagging",
          "s3:GetObjectVersionTorrent",
          "s3:PutObject",
          "s3:GetObjectAcl",
          "s3:GetObject",
          "s3:GetObjectTorrent",
          "s3:AbortMultipartUpload",
          "s3:PutObjectVersionAcl",
          "s3:GetObjectVersionAcl",
          "s3:GetObjectTagging",
          "s3:PutObjectTagging",
          "s3:DeleteObject",
          "s3:PutObjectAcl",
          "s3:GetObjectVersion",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::blinkit-data-staging",
          "arn:aws:s3:::blinkit-data-staging/*",
          "arn:aws:s3:::blinkit-derived-etls",
          "arn:aws:s3:::blinkit-derived-etls/*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "role-policy-attachment" {
  role       = aws_iam_role.serviceaccount-role.name
  policy_arn = aws_iam_policy.role-policy.arn
}
