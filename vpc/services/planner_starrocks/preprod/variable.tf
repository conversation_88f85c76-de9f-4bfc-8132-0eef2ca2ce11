variable "iam_prefix" {
  description = "IAM prefix. As IAM name will be combination `iam_prefix-environment`"
  type        = string
}

variable "service_name" {
  description = "Service Name"
  type        = string
}

variable "service_namespace" {
  description = "Service Namespace"
  type        = string
}

variable "environment" {
  description = "Name of environment: prod, etc"
  type        = string
  default     = "preprod"
}
variable "vpc_id" {
  description = "The ID of the VPC where planner will be deployed"
  type        = string
}