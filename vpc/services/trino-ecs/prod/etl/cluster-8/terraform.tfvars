aws_provider_region = "ap-southeast-1"
aws_profile        = "blinkit_analytics"
environment        = "prod"
cluster_number     = "8"
cluster_type       = "etl"

# ECS cluster name (this should be the existing ECS cluster where services will run)
ecs_cluster_name = "etl-trino"

# IAM roles (same as ETL 7 based on the JSON)
task_role_arn     = "arn:aws:iam::183295456051:role/prod-blinkit-trino-role"
execution_role_arn = "arn:aws:iam::183295456051:role/ecsTaskExecutionRole"

# Docker images (same as ETL 7 based on the JSON)
trino_image            = "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/trinodb/etl:v455_f06234842ef7"
fluentd_image          = "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/fluentd:v1.13-trino_bebd2b9134ed"
cloudwatch_agent_image = "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/cloudposse:0.14.0_15a30d4c64b0"

# Launch Template Configuration
amd_launch_template_id   = "lt-097a3499c11fbdeac"
arm_launch_template_id   = "lt-0095e2194b4d73e97"
amd_launch_template_name = "blinkit-etl-trino-amd-lt"
arm_launch_template_name = "blinkit-etl-trino-arm-lt"

# Service Discovery Configuration (using same namespace as ETL 7)
# You need to get this ID from: aws servicediscovery list-namespaces --query 'Namespaces[?Name==`trino`].Id' --output text
service_discovery_namespace_id = "ns-zewzhriv7rr66pk2"

# Network configuration
subnet_ids         = ["subnet-091d72e204a90b03e"]
security_group_ids = ["sg-0fa1113efaa345a7d"] 