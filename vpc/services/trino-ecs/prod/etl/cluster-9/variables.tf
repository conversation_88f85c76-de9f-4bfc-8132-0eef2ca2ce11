variable "aws_provider_region" {
  type        = string
  description = "AWS region for the provider"
}

variable "aws_profile" {
  type        = string
  description = "AWS profile to use for authentication"
}

variable "environment" {
  type        = string
  description = "Environment name (prod, preprod, dev)"
}

variable "cluster_number" {
  type        = string
  description = "Cluster number (9)"
}

variable "cluster_type" {
  type        = string
  description = "Cluster type (etl, adhoc, reporting)"
}

variable "ecs_cluster_name" {
  type        = string
  description = "Name of the ECS cluster to use"
}

variable "task_role_arn" {
  type        = string
  description = "ARN of the IAM role for ECS tasks"
}

variable "execution_role_arn" {
  type        = string
  description = "ARN of the IAM role for ECS task execution"
}

variable "trino_image" {
  type        = string
  description = "Trino Docker image to use"
}

variable "fluentd_image" {
  type        = string
  description = "Fluentd Docker image to use"
}

variable "cloudwatch_agent_image" {
  type        = string
  description = "CloudWatch agent Docker image to use"
}

variable "subnet_ids" {
  type        = list(string)
  description = "List of subnet IDs for ECS services"
}

variable "security_group_ids" {
  type        = list(string)
  description = "List of security group IDs for ECS services"
}

# Launch Template Variables
variable "amd_launch_template_id" {
  type        = string
  description = "Launch template ID for AMD instances"
}

variable "arm_launch_template_id" {
  type        = string
  description = "Launch template ID for ARM instances"
}

variable "amd_launch_template_name" {
  type        = string
  description = "Launch template name for AMD instances"
}

variable "arm_launch_template_name" {
  type        = string
  description = "Launch template name for ARM instances"
}

# Service Discovery Variables
variable "service_discovery_namespace_id" {
  type        = string
  description = "The ID of the existing 'trino' private DNS namespace for service discovery"
}

# Local values for dynamic naming
locals {
  # Base naming prefix
  name_prefix = "blinkit-${var.cluster_type}-trino"
  name_prefix_service = "blinkit-${var.cluster_type}"
  
  # ASG names
  coordinator_asg_name = "${local.name_prefix}-coordinator-${var.cluster_number}-mixed-arch-asg"
  worker_asg_name      = "${local.name_prefix}-worker-${var.cluster_number}-mixed-arch-asg"

  
  # Service names (for ECS services)
  coordinator_service_name = "${local.name_prefix_service}-coordinator-${var.cluster_number}"
  worker_service_name      = "${local.name_prefix_service}-worker-${var.cluster_number}"
  
  # Service Discovery names
  coordinator_service_discovery_name = "${local.name_prefix_service}-coordinator-${var.cluster_number}"
  
  # Discovery URI for Trino configuration
  coordinator_discovery_uri = "http://${local.coordinator_service_discovery_name}.trino:8889"
  
  # Lifecycle hook name
  worker_lifecycle_hook_name = "terminate-instance-hook"
  
  # Common tags
  common_tags = {
    Environment              = var.environment
    ClusterType              = var.cluster_type
    ClusterNumber            = var.cluster_number
    "cost:application"       = "trino-${var.cluster_type}"
    "blinkit:resource_group" = "trino"
    "blinkit:terraform"      = "true"
  }
} 