terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.55.0"
    }
  }
  backend "s3" {
    bucket  = "blinkit-analytics-terraform"
    key     = "banalytics-infrastructure/blinkit-stack/trino-ecs/etl/cluster-9/terraform.tfstate"
    region  = "ap-southeast-1"
    profile = "blinkit_analytics"
  }
}

provider "aws" {
  region  = var.aws_provider_region
  profile = var.aws_profile
} 