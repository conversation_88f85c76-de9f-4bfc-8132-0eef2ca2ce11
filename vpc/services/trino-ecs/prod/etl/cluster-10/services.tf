# ECS Service for Coordinator
resource "aws_ecs_service" "blinkit_etl_coordinator_10" {
  name                               = local.coordinator_service_name
  cluster                            = data.aws_ecs_cluster.main.id
  task_definition                    = aws_ecs_task_definition.blinkit_trino_etl_coordinator_10.family
  desired_count                      = 1
  scheduling_strategy                = "REPLICA"
  deployment_maximum_percent         = 100
  deployment_minimum_healthy_percent = 0

  deployment_controller {
    type = "ECS"
  }

  network_configuration {
    subnets         = [data.aws_subnet.trino_subnet.id]
    security_groups = [data.aws_security_group.trino_security_group.id]
  }

  # Service Discovery Integration
  # This registers the coordinator service so workers can discover it via DNS
  service_registries {
    registry_arn = aws_service_discovery_service.coordinator.arn
  }

  placement_constraints {
    type = "distinctInstance"
  }

  enable_ecs_managed_tags = true

  tags = merge(local.common_tags, {
    Name             = local.coordinator_service_name
    "cost:component" = "ecs-service"
  })

  lifecycle {
    ignore_changes = [
      desired_count,
      task_definition,
      launch_type,
    ]
  }
}

# ECS Service for Worker
resource "aws_ecs_service" "blinkit_etl_worker_10" {
  name                               = local.worker_service_name
  cluster                            = data.aws_ecs_cluster.main.id
  task_definition                    = aws_ecs_task_definition.blinkit_trino_etl_worker_10.family
  desired_count                      = 16
  scheduling_strategy                = "REPLICA"
  deployment_maximum_percent         = 200
  deployment_minimum_healthy_percent = 100

  deployment_controller {
    type = "ECS"
  }

  # Worker uses host networking, so no network_configuration block needed

  enable_ecs_managed_tags = true


  ordered_placement_strategy {
    type  = "spread"
    field = "attribute:ecs.availability-zone"
  }

  ordered_placement_strategy {
    type  = "spread"
    field = "instanceId"
  }

  ordered_placement_strategy {
    type  = "binpack"
    field = "memory"
  }

  tags = merge(local.common_tags, {
    Name             = local.worker_service_name
    "cost:component" = "ecs-service"
  })

  lifecycle {
    ignore_changes = [
      desired_count,
      task_definition,
      launch_type,
    ]
  }
} 