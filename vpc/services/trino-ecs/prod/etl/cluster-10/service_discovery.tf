# Service Discovery for Coordinator
# This creates a service discovery entry that allows worker nodes to find the coordinator
# Using the existing "trino" private DNS namespace that ETL 7 already uses

resource "aws_service_discovery_service" "coordinator" {
  name = local.coordinator_service_discovery_name

  dns_config {
    namespace_id = var.service_discovery_namespace_id
    
    dns_records {
      ttl  = 60
      type = "A"
    }
    
    routing_policy = "MULTIVALUE"
  }

  health_check_custom_config {
    failure_threshold = 1
  }

  tags = merge(local.common_tags, {
    Name             = local.coordinator_service_discovery_name
    "cost:component" = "service-discovery"
  })
} 