# Task Definition for Coordinator
resource "aws_ecs_task_definition" "blinkit_trino_etl_coordinator_10" {
  family                   = "blinkit_trino_etl_coordinator_10_mixed_arch"
  network_mode             = "awsvpc"
  requires_compatibilities = ["EC2"]
  task_role_arn           = data.aws_iam_role.task_role.arn
  execution_role_arn      = data.aws_iam_role.execution_role.arn

  volume {
    name = "extra_etc"
    host_path = "/root/extra_etc"
  }

  container_definitions = jsonencode([
    {
      name              = "trino_coordinator"
      image             = var.trino_image
      cpu               = 0
      memoryReservation = 2048
      essential         = true
      
      portMappings = [
        {
          name          = "trino_coordinator-8889-tcp"
          containerPort = 8889
          hostPort      = 8889
          protocol      = "tcp"
        },
        {
          name          = "trino_coordinator-7071-tcp"
          containerPort = 7071
          hostPort      = 7071
          protocol      = "tcp"
        }
      ]

      environment = [
        { name = "RETRY_POLICY", value = "QUERY" },
        { name = "BLINKIT_HIVE_QUERY_PARTITION_FILTER_REQUIRED", value = "true" },
        { name = "BLINKIT_ICEBERG_EXTENDED_STATISTICS_ENABLED", value = "false" },
        { name = "ZOMATO_PROD_HIVE_METASTORE_URI", value = "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9085" },
        { name = "HTTP_EVENT_LISTENER_SPLIT_EVENTS", value = "false" },
        { name = "BLINKIT_ICEBERG_QUERY_PARTITION_FILTER_REQUIRED", value = "true" },
        { name = "QUERY_MAX_MEMORY", value = "300GB" },
        { name = "DISTRICT_PROD_ICEBERG_METASTORE_URI", value = "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9086" },
        { name = "QUERY_MAX_SCAN_PHYSICAL_BYTES", value = "1TB" },
        { name = "TRINO_CHECKPOINTS_BASE_DIRECTORIES", value = "s3://trino-metadata" },
        { name = "HTTP_EVENT_LISTENER_QUERY_COMPLETED", value = "false" },
        { name = "HTTP_EVENT_LISTENER_ENDPOINT", value = "http://172.31.103.244:8080/v1/eventsource/receiver" },
        { name = "BLINKIT_ICEBERG_EXTENDED_STATISTICS_COLLECT_ON_WRITE", value = "false" },
        { name = "HTTP_EVENT_LISTENER_QUERY_CREATED", value = "true" },
        { name = "CLUSTER_NAME", value = "blinkit-etl-cluster-10" },
        { name = "ICEBERG_WORKER_NUM_THREADS_CUSTOM", value = "4" },
        { name = "BLINKIT_PROD_HIVE_METASTORE_URI", value = "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083" },
        { name = "AUTHENTICATION_TYPE", value = "PASSWORD" },
        { name = "QUERY_MAX_RUN_TIME", value = "70m" },
        { name = "QUERY_MAX_LENGTH", value = "1000000" },
        { name = "BLINKIT_HUDI_QUERY_PARTITION_FILTER_REQUIRED", value = "true" },
        { name = "PARTITION_PROJECTION", value = "true" },
        { name = "TENANT", value = "BLINKIT" },
        { name = "BLINKIT_PROD_ICEBERG_METASTORE_URI", value = "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9084" },
        { name = "QUERY_MAX_EXECUTION_TIME", value = "50m" },
        { name = "COORDINATOR", value = "true" },
        { name = "DISCOVERY_URI", value = local.coordinator_discovery_uri },
        { name = "ZOMATO_STAG_HIVE_METASTORE_URI", value = "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9083" },
        { name = "BLINKIT_HIVE_MAX_PARTITIONS_PER_SCAN", value = "500" },
        { name = "DISTRICT_PROD_METASTORE_URI", value = "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9086" },
        { name = "ZOMATO_PROD_ICEBERG_METASTORE_URI", value = "thrift://vpce-0119d495a1350b5bf-tk21ss9u.vpce-svc-04280f98f05f90b0f.ap-southeast-1.vpce.amazonaws.com:9085" },
        { name = "BLINKIT_PROD_HUDI_METASTORE_URI", value = "thrift://datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io:9083" }
      ]

      mountPoints = [
        {
          sourceVolume  = "extra_etc"
          containerPath = "/usr/lib/trino/extra_etc"
          readOnly      = true
        }
      ]

      secrets = [
        {
          name      = "PASSWORD_FILE"
          valueFrom = "/services/trino/prod/blinkit/etl/coordinator/BLINKIT_TRINO_USERS"
        },
        {
          name      = "SHARED_SECRET"
          valueFrom = "/services/trino/prod/blinkit/etl/coordinator/SHARED_SECRET"
        }
      ]

      dockerLabels = {
        "PROMETHEUS_EXPORTER_PORT"     = "7071"
        "service-name"                 = "trino-cluster"
        "environment"                  = "prod"
        "PROMETHEUS_EXPORTER_PATH"     = "/metrics"
        "service"                      = "prod-trino-ecs"
        "PROMETHEUS_EXPORTER_JOB_NAME" = "prod-trino-ecs"
        "tenant"                       = "blinkit"
      }

      ulimits = [
        {
          name      = "nofile"
          softLimit = 1048576
          hardLimit = 1048576
        },
        {
          name      = "nproc"
          softLimit = 1048576
          hardLimit = 1048576
        }
      ]

      logConfiguration = {
        logDriver = "awslogs"
        options = {
          "awslogs-group"         = "/ecs/blinkit_trino_etl_coordinator_10"
          "awslogs-region"        = "ap-southeast-1"
          "awslogs-stream-prefix" = "ecs"
        }
      }
    },
    {
      name              = "fluentd"
      image             = var.fluentd_image
      cpu               = 0
      memory            = 512
      memoryReservation = 128
      essential         = false

      portMappings = [
        {
          name          = "fluentd-24224-tcp"
          containerPort = 24224
          hostPort      = 24224
          protocol      = "tcp"
        }
      ]

      environment = [
        { name = "FLUENTD_BUCKET", value = "zanalytics-logs" },
        { name = "RUBYLIB", value = "/usr/lib/ruby/gems/2.7.0/gems/resolv-0.2.1/lib" }
      ]

      dependsOn = [
        {
          containerName = "trino_coordinator"
          condition     = "START"
        }
      ]
    },
    {
      name      = "cloudwatch-agent-prometheus"
      image     = var.cloudwatch_agent_image
      cpu       = 0
      memory    = 128
      essential = false

      environment = [
        { name = "CLOUDWATCH_NAMESPACE", value = "TrinoClusterMetrics" },
        { name = "PROMETHEUS_SCRAPE_INTERVAL", value = "60" },
        { name = "CLOUDWATCH_REGION", value = "ap-southeast-1" },
        { name = "ADDITIONAL_DIMENSION", value = "cluster=blinkit-etl-ecs-trino-10" },
        { name = "CLOUDWATCH_PUBLISH_TIMEOUT", value = "5" },
        { name = "PROMETHEUS_SCRAPE_URL", value = "http://blinkit-etl-coordinator-10.trino:7071/metrics" },
        { name = "INCLUDE_METRICS", value = "trino_execution_querymanager_queuedqueries,trino_execution_querymanager_runningqueries,trino_execution_clustersizemonitor_requiredworkers,trino_metadata_discoverynodemanager_activenodecount,trino_metadata_discoverynodemanager_shuttingdownnodecount,trino_metadata_discoverynodemanager_inactivenodecount" }
      ]

      dependsOn = [
        {
          containerName = "trino_coordinator"
          condition     = "START"
        }
      ]
    }
  ])

  tags = {
    Name                     = "blinkit_trino_etl_coordinator_10_mixed_arch"
    Environment              = var.environment
    ClusterType              = var.cluster_type
    ClusterNumber            = var.cluster_number
    "blinkit:terraform"      = "true"
    "ecs:taskDefinition:createdFrom" = "terraform"
  }
}

# Task Definition for Worker
resource "aws_ecs_task_definition" "blinkit_trino_etl_worker_10" {
  family                   = "blinkit_trino_etl_worker_10"
  network_mode             = "host"
  requires_compatibilities = ["EC2"]
  task_role_arn           = data.aws_iam_role.task_role.arn
  execution_role_arn      = data.aws_iam_role.execution_role.arn

  volume {
    name = "extra_etc"
    host_path = "/root/extra_etc"
  }

  container_definitions = jsonencode([
    {
      name              = "trino_worker"
      image             = var.trino_image
      cpu               = 0
      memoryReservation = 1024
      essential         = true

      portMappings = [
        {
          name          = "trino_worker-8889-tcp"
          containerPort = 8889
          hostPort      = 8889
          protocol      = "tcp"
        }
      ]

      environment = [
        { name = "BLINKIT_ICEBERG_EXTENDED_STATISTICS_COLLECT_ON_WRITE", value = "false" },
        { name = "RETRY_POLICY", value = "QUERY" },
        { name = "ICEBERG_WORKER_NUM_THREADS_CUSTOM", value = "4" },
        { name = "QUERY_MAX_EXECUTION_TIME", value = "50m" },
        { name = "COORDINATOR", value = "false" },
        { name = "QUERY_MAX_RUN_TIME", value = "70m" },
        { name = "DISCOVERY_URI", value = local.coordinator_discovery_uri },
        { name = "QUERY_MAX_SCAN_PHYSICAL_BYTES", value = "500GB" },
        { name = "QUERY_MAX_LENGTH", value = "1000000" },
        { name = "TRINO_CHECKPOINTS_BASE_DIRECTORIES", value = "s3://trino-metadata/tardigrade_400_checkpoints_01/,s3://trino-metadata/tardigrade_400_checkpoints_02/,s3://trino-metadata/tardigrade_400_checkpoints_03/,s3://trino-metadata/tardigrade_400_checkpoints_04/,s3://trino-metadata/tardigrade_400_checkpoints_05/,s3://trino-metadata/tardigrade_400_checkpoints_06/,s3://trino-metadata/tardigrade_400_checkpoints_07/,s3://trino-metadata/tardigrade_400_checkpoints_08/,s3://trino-metadata/tardigrade_400_checkpoints_09/,s3://trino-metadata/tardigrade_400_checkpoints_10/" },
        { name = "TENANT", value = "BLINKIT" }
      ]

      mountPoints = [
        {
          sourceVolume  = "extra_etc"
          containerPath = "/usr/lib/trino/extra_etc"
          readOnly      = true
        }
      ]

      secrets = [
        {
          name      = "SHARED_SECRET"
          valueFrom = "/services/trino/prod/blinkit/etl/coordinator/SHARED_SECRET"
        }
      ]

      stopTimeout = 1800

      dockerLabels = {
        "PROMETHEUS_EXPORTER_PORT"     = "7071"
        "service-name"                 = "trino-cluster"
        "environment"                  = "prod"
        "PROMETHEUS_EXPORTER_PATH"     = "/metrics"
        "service"                      = "prod-trino-ecs"
        "PROMETHEUS_EXPORTER_JOB_NAME" = "prod-trino-ecs"
        "tenant"                       = "blinkit"
      }

      ulimits = [
        {
          name      = "nofile"
          softLimit = 1048576
          hardLimit = 1048576
        },
        {
          name      = "nproc"
          softLimit = 1048576
          hardLimit = 1048576
        }
      ]
    }
  ])

  tags = {
    Name                     = "blinkit_trino_etl_worker_10"
    Environment              = var.environment
    ClusterType              = var.cluster_type
    ClusterNumber            = var.cluster_number
    "blinkit:terraform"      = "true"
  }
} 