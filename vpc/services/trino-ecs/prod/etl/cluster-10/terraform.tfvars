aws_provider_region = "ap-southeast-1"
aws_profile        = "blinkit_analytics"
environment        = "prod"
cluster_number     = "10"
cluster_type       = "etl"

ecs_cluster_name = "etl-trino"

trino_image            = "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/trinodb/etl:v455_f06234842ef7"
fluentd_image          = "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/fluentd:v1.13-trino_bebd2b9134ed"
cloudwatch_agent_image = "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/cloudposse:0.14.0_15a30d4c64b0"

service_discovery_namespace_id = "ns-zewzhriv7rr66pk2" 