terraform {
  required_providers {
    aws = {
      version = "4.4.0"
    }
  }
  backend "s3" {
    bucket  = "blinkit-analytics-terraform"
    key     = "banalytics-infrastructure/blinkit-stack/trino-preprod-apse1-analytics/trino-preprod.tfstate"
    region  = "ap-southeast-1"
    profile = "blinkit_analytics"
  }
}
provider "aws" {
  region  = var.aws_provider_region
  profile = var.aws_profile
}
