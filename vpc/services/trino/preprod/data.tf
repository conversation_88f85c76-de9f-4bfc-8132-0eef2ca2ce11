data "aws_subnet" "trino_subnet" {
  id = "subnet-091d72e204a90b03e"
}

data "aws_subnet" "trino_subnet_etl" {
  id = "subnet-06b312b17ae4764a3"
}

data "aws_ami" "ecs_amd_ami" {
  most_recent = true
  owners      = ["self", "amazon"]
  filter {
    name   = "image-id"
    values = [var.amd_ami_id]
  }
}

data "aws_ami" "ecs_arm_ami" {
  most_recent = true
  owners      = ["self", "amazon"]
  filter {
    name   = "image-id"
    values = [var.arm_ami_id]
  }
}

data "aws_iam_instance_profile" "ecs_instance_role" {
  name = "ecsInstanceRole"
}
