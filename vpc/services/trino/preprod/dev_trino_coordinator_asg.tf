resource "aws_autoscaling_group" "dev_trino_coordinator_asg" {
    capacity_rebalance        = true
    default_cooldown          = 60
    desired_capacity          = 0
    enabled_metrics           = []
    health_check_grace_period = 60
    health_check_type         = "EC2"
    load_balancers            = []
    max_instance_lifetime     = 0
    max_size                  = 1
    metrics_granularity       = "1Minute"
    min_size                  = 0
    name                      = "dev-trino-coordinator-mixed-arch-asg"
    protect_from_scale_in     = true
    suspended_processes       = []
    target_group_arns         = []
    termination_policies      = []
    vpc_zone_identifier       = [data.aws_subnet.trino_subnet.id]

    mixed_instances_policy {
        instances_distribution {
            on_demand_allocation_strategy            = "prioritized"
            on_demand_base_capacity                  = 0
            on_demand_percentage_above_base_capacity = 0
            spot_allocation_strategy                 = "price-capacity-optimized"
            spot_instance_pools                      = 0
        }

        launch_template {
            launch_template_specification {
                launch_template_id   = "lt-08d766832d5a44d32"
                launch_template_name = "dev-trino-arm-lt"
                version              = "$Default"
            }

            override {
                instance_type = "c7g.4xlarge"
            }
            override {
                instance_type = "c6g.4xlarge"
            }
        }
    }

    tag {
        key                 = "AmazonECSManaged"
        propagate_at_launch = true
        value               = ""
    }

    timeouts {}
}