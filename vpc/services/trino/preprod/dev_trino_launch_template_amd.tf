resource "aws_launch_template" "dev_trino_lt_amd" {
  name                   = "dev-trino-amd-lt"
  image_id               = data.aws_ami.ecs_amd_ami.id
  vpc_security_group_ids = ["sg-0276cae8e833df2e6", "sg-0fa1113efaa345a7d", ]
  key_name               = "prod-trino"
  user_data              = "IyEvYmluL2Jhc2gKc2V0IC1ldXhvIHBpcGVmYWlsCgplY2hvIEVDU19DTFVTVEVSPWRldi10cmlubyA+PiAvZXRjL2Vjcy9lY3MuY29uZmlnCmVjaG8gRUNTX0JBQ0tFTkRfSE9TVD0gPj4gL2V0Yy9lY3MvZWNzLmNvbmZpZwplY2hvIEVDU19JTUFHRV9DTEVBTlVQX0lOVEVSVkFMPTEwbWlucyA+PiAvZXRjL2Vjcy9lY3MuY29uZmlnOwplY2hvIEVDU19FTkdJTkVfVEFTS19DTEVBTlVQX1dBSVRfRFVSQVRJT049MTBtaW5zID4+IC9ldGMvZWNzL2Vjcy5jb25maWc7CmVjaG8gRUNTX0lNQUdFX01JTklNVU1fQ0xFQU5VUF9BR0U9MTVtaW5zID4+IC9ldGMvZWNzL2Vjcy5jb25maWc7CmVjaG8gRUNTX0VOQUJMRV9TUE9UX0lOU1RBTkNFX0RSQUlOSU5HPXRydWUgPj4gL2V0Yy9lY3MvZWNzLmNvbmZpZzsKSU5TVEFOQ0VfSUQ9JChjYXQgL3Zhci9saWIvY2xvdWQvZGF0YS9pbnN0YW5jZS1pZCk7CmVjaG8gRUNTX0lOU1RBTkNFX0FUVFJJQlVURVM9e1wiZWMyX2luc3RhbmNlXCI6XCIkSU5TVEFOQ0VfSURcIn0gPj4gL2V0Yy9lY3MvZWNzLmNvbmZpZzsKCnl1bSBpbnN0YWxsICAteSBhd3MtY2xpCm1rZGlyIC1wIC9yb290L2V4dHJhX2V0YwoKQkFTRV9TM19CVUNLRVRfUEFUSD0nemFuYWx5dGljcycKQ0xVU1RFUl9UWVBFPSdhZGhvYy1jbHVzdGVyJwoKYXdzIHMzIGNwIHMzOi8vJHtCQVNFX1MzX0JVQ0tFVF9QQVRIfS9jb25maWcvZW1yL3RyaW5vZGIvIC9yb290L2V4dHJhX2V0Yy8gLS1yZWN1cnNpdmUgLS1leGNsdWRlICcqJyAgLS1pbmNsdWRlICcqcnVsZXMuanNvbicKYXdzIHMzIGNwIHMzOi8vJHtCQVNFX1MzX0JVQ0tFVF9QQVRIfS90cmlub2RiL2dyb3VwLnR4dCAvcm9vdC9leHRyYV9ldGMvZ3JvdXAudHh0Ci9iaW4vYmFzaCAtYyAiKGNyb250YWIgLWwgMj4vZGV2L251bGw7IGVjaG8gJyogKiAqICogKiBhd3MgczMgY3AgczM6Ly8ke0JBU0VfUzNfQlVDS0VUX1BBVEh9L2NvbmZpZy9lbXIvdHJpbm9kYi8gL3Jvb3QvZXh0cmFfZXRjLyAtLXJlY3Vyc2l2ZSAtLWV4Y2x1ZGUgXCIqXCIgIC0taW5jbHVkZSBcIipydWxlcy5qc29uXCIgPj4gL3Jvb3QvcnVsZXMubG9nICAyPiYxJykgfCBjcm9udGFiIC0iCi9iaW4vYmFzaCAtYyAiKGNyb250YWIgLWwgMj4vZGV2L251bGw7IGVjaG8gJyogKiAqICogKiBhd3MgczMgY3AgczM6Ly8ke0JBU0VfUzNfQlVDS0VUX1BBVEh9L3RyaW5vZGIvZ3JvdXAudHh0IC9yb290L2V4dHJhX2V0Yy9ncm91cC50eHQgPj4gL3Jvb3QvZ3JvdXAubG9nIDI+JjEnKSB8IGNyb250YWIgLSIKCmF3cyBzMyBjcCBzMzovL3phbmFseXRpY3MvY29uZmlnL2Vtci9jb21tb24vc2NyaXB0cy9hbXpuMi9ib290c3RyYXAtZXNzZW50aWFscy10aW1lem9uZS5zaCAgL3Jvb3QvYm9vdHN0cmFwLWVzc2VudGlhbHMtdGltZXpvbmUuc2gKYmFzaCAvdG9vdC9ib290c3RyYXAtZXNzZW50aWFscy10aW1lem9uZS5zaAo="  
  
  iam_instance_profile {
    arn = data.aws_iam_instance_profile.ecs_instance_role.arn
  }

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "true"
      iops                  = 3000
      snapshot_id           = "snap-02a032375f7996f63"
      throughput            = 125
      volume_size           = 256
      volume_type           = "gp3"
    }
  }

  monitoring {
    enabled = true
  }

  default_version = 2
}
