resource "aws_autoscaling_group" "blinkit_adhoc_worker_2_asg" {
  capacity_rebalance = true
  default_cooldown   = 60
  desired_capacity   = 22
  enabled_metrics = [
    "GroupAndWarmPoolDesiredCapacity",
    "GroupAndWarmPoolTotalCapacity",
    "GroupDesiredCapacity",
    "GroupInServiceCapacity",
    "GroupInServiceInstances",
    "GroupMaxSize",
    "GroupMinSize",
    "GroupPendingCapacity",
    "GroupPendingInstances",
    "GroupStandbyCapacity",
    "GroupStandbyInstances",
    "GroupTerminatingCapacity",
    "GroupTerminatingInstances",
    "GroupTotalCapacity",
    "GroupTotalInstances",
    "WarmPoolDesiredCapacity",
    "WarmPoolMinSize",
    "WarmPoolPendingCapacity",
    "WarmPoolTerminatingCapacity",
    "WarmPoolTotalCapacity",
    "WarmPoolWarmedCapacity",
  ]
  health_check_grace_period = 60
  health_check_type         = "EC2"
  load_balancers            = []
  max_instance_lifetime     = 0
  max_size                  = 30
  metrics_granularity       = "1Minute"
  min_size                  = 0
  name                      = "blinkit-adhoc-trino-worker-2-mixed-arch-asg"
  protect_from_scale_in     = true
  suspended_processes       = []
  target_group_arns         = []
  termination_policies      = []
  vpc_zone_identifier       = [data.aws_subnet.trino_subnet.id]

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy            = "lowest-price"
      on_demand_base_capacity                  = 0
      on_demand_percentage_above_base_capacity = 0
      spot_allocation_strategy                 = "capacity-optimized"
      spot_instance_pools                      = 0
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = "lt-0e9987f09fa38a2e2"
        launch_template_name = "blinkit-adhoc-trino-amd-lt"
        version              = "$Default"
      }
    }
  }

  tag {
    key                 = "AmazonECSManaged"
    propagate_at_launch = true
    value               = ""
  }

  timeouts {}

  lifecycle {
    ignore_changes = [
      desired_capacity,
      max_size,
      min_size,
      mixed_instances_policy[0].instances_distribution[0].on_demand_percentage_above_base_capacity
    ]
  }
}

resource "aws_autoscaling_lifecycle_hook" "blinkit_adhoc_worker_2_lifecycle_hook" {
  name                   = "terminate-instance-hook"
  autoscaling_group_name = "blinkit-adhoc-trino-worker-2-mixed-arch-asg"
  default_result         = "CONTINUE" # or "ABANDON" to terminate immediately

  heartbeat_timeout    = 300 # Timeout in seconds before the instance is terminated
  lifecycle_transition = "autoscaling:EC2_INSTANCE_TERMINATING"
}
