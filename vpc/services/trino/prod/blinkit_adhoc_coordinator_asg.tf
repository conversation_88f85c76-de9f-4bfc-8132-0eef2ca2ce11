resource "aws_autoscaling_group" "blinkit_adhoc_coordinator_asg" {
  capacity_rebalance        = false
  default_cooldown          = 300
  desired_capacity          = 1
  enabled_metrics           = []
  health_check_grace_period = 300
  health_check_type         = "EC2"
  load_balancers            = []
  max_instance_lifetime     = 0
  max_size                  = 2
  metrics_granularity       = "1Minute"
  min_size                  = 0
  name                      = "blinkit-adhoc-trino-coordinator-mixed-arch-asg"
  protect_from_scale_in     = true
  suspended_processes       = []
  target_group_arns         = []
  termination_policies      = []
  vpc_zone_identifier       = [data.aws_subnet.trino_subnet.id]

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy            = "prioritized"
      on_demand_base_capacity                  = 0
      on_demand_percentage_above_base_capacity = 100
      spot_allocation_strategy                 = "price-capacity-optimized"
      spot_instance_pools                      = 0
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = "lt-0c49e46c6768a6a93"
        launch_template_name = "blinkit-adhoc-trino-arm-lt"
        version              = "$Default"
      }

      override {
        instance_type = "c7g.4xlarge"
      }
      override {
        instance_type = "c6g.4xlarge"
      }
    }
  }

  tag {
    key                 = "AmazonECSManaged"
    propagate_at_launch = true
    value               = ""
  }

  timeouts {}
}
