resource "aws_launch_template" "blinkit_adhoc_lt_amd" {
  name                   = "blinkit-adhoc-trino-amd-lt"
  image_id               = data.aws_ami.ecs_amd_ami.id
  vpc_security_group_ids = ["sg-0276cae8e833df2e6", "sg-0fa1113efaa345a7d", ]
  key_name               = "prod-trino"
  user_data              = "IyEvYmluL2Jhc2gKc2V0IC1ldXhvIHBpcGVmYWlsCgplY2hvIEVDU19DTFVTVEVSPWFkaG9jLXRyaW5vID4+IC9ldGMvZWNzL2Vjcy5jb25maWcKZWNobyBFQ1NfQkFDS0VORF9IT1NUPSA+PiAvZXRjL2Vjcy9lY3MuY29uZmlnCmVjaG8gRUNTX0lNQUdFX0NMRUFOVVBfSU5URVJWQUw9MTBtaW5zID4+IC9ldGMvZWNzL2Vjcy5jb25maWc7CmVjaG8gRUNTX0VOR0lORV9UQVNLX0NMRUFOVVBfV0FJVF9EVVJBVElPTj0xMG1pbnMgPj4gL2V0Yy9lY3MvZWNzLmNvbmZpZzsKZWNobyBFQ1NfSU1BR0VfTUlOSU1VTV9DTEVBTlVQX0FHRT0xNW1pbnMgPj4gL2V0Yy9lY3MvZWNzLmNvbmZpZzsKZWNobyBFQ1NfRU5BQkxFX1NQT1RfSU5TVEFOQ0VfRFJBSU5JTkc9dHJ1ZSA+PiAvZXRjL2Vjcy9lY3MuY29uZmlnOwpJTlNUQU5DRV9JRD0kKGNhdCAvdmFyL2xpYi9jbG91ZC9kYXRhL2luc3RhbmNlLWlkKTsKZWNobyBFQ1NfSU5TVEFOQ0VfQVRUUklCVVRFUz17XCJlYzJfaW5zdGFuY2VcIjpcIiRJTlNUQU5DRV9JRFwifSA+PiAvZXRjL2Vjcy9lY3MuY29uZmlnOwoKeXVtIGluc3RhbGwgIC15IGF3cy1jbGkKbWtkaXIgLXAgL3Jvb3QvZXh0cmFfZXRjCgpCQVNFX1MzX0JVQ0tFVF9QQVRIPSd6YW5hbHl0aWNzJwpDTFVTVEVSX1RZUEU9J2FkaG9jLWNsdXN0ZXInCgphd3MgczMgY3AgczM6Ly8ke0JBU0VfUzNfQlVDS0VUX1BBVEh9L2NvbmZpZy9lbXIvdHJpbm9kYi8gL3Jvb3QvZXh0cmFfZXRjLyAtLXJlY3Vyc2l2ZSAtLWV4Y2x1ZGUgJyonICAtLWluY2x1ZGUgJypydWxlcy5qc29uJwphd3MgczMgY3AgczM6Ly8ke0JBU0VfUzNfQlVDS0VUX1BBVEh9L3RyaW5vZGIvZ3JvdXAudHh0IC9yb290L2V4dHJhX2V0Yy9ncm91cC50eHQKL2Jpbi9iYXNoIC1jICIoY3JvbnRhYiAtbCAyPi9kZXYvbnVsbDsgZWNobyAnKiAqICogKiAqIGF3cyBzMyBjcCBzMzovLyR7QkFTRV9TM19CVUNLRVRfUEFUSH0vY29uZmlnL2Vtci90cmlub2RiLyAvcm9vdC9leHRyYV9ldGMvIC0tcmVjdXJzaXZlIC0tZXhjbHVkZSAiJyIqIiciICAtLWluY2x1ZGUgIiciKnJ1bGVzLmpzb24iJyIgPj4gL3Jvb3QvcnVsZXMubG9nICAyPiYxJykgfCBjcm9udGFiIC0iCi9iaW4vYmFzaCAtYyAiKGNyb250YWIgLWwgMj4vZGV2L251bGw7IGVjaG8gJyogKiAqICogKiBhd3MgczMgY3AgczM6Ly8ke0JBU0VfUzNfQlVDS0VUX1BBVEh9L3RyaW5vZGIvZ3JvdXAudHh0IC9yb290L2V4dHJhX2V0Yy9ncm91cC50eHQgPj4gL3Jvb3QvZ3JvdXAubG9nIDI+JjEnKSB8IGNyb250YWIgLSIKCmF3cyBzMyBjcCBzMzovL3phbmFseXRpY3MvY29uZmlnL2Vtci9jb21tb24vc2NyaXB0cy9hbXpuMi9ib290c3RyYXAtZXNzZW50aWFscy10aW1lem9uZS5zaAkgL3Jvb3QvYm9vdHN0cmFwLWVzc2VudGlhbHMtdGltZXpvbmUuc2gKYmFzaCAvcm9vdC9ib290c3RyYXAtZXNzZW50aWFscy10aW1lem9uZS5zaA=="

  iam_instance_profile {
    arn = data.aws_iam_instance_profile.ecs_instance_role.arn
  }

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "true"
      iops                  = 3000
      snapshot_id           = "snap-02a032375f7996f63"
      throughput            = 125
      volume_size           = 256
      volume_type           = "gp3"
    }
  }

  monitoring {
    enabled = true
  }
}