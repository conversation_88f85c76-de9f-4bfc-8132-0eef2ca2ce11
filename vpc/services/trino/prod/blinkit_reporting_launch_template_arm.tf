resource "aws_launch_template" "blinkit_reporting_launch_template_arm" {
  image_id  = data.aws_ami.ecs_arm_ami.id
  key_name  = "prod-trino"
  name      = "blinkit-reporting-trino-arm-lt"
  user_data = "IyEvYmluL2Jhc2gKc2V0IC1ldXhvIHBpcGVmYWlsCgplY2hvIEVDU19DTFVTVEVSPXJlcG9ydGluZy10cmlubyA+PiAvZXRjL2Vjcy9lY3MuY29uZmlnCmVjaG8gRUNTX0JBQ0tFTkRfSE9TVD0gPj4gL2V0Yy9lY3MvZWNzLmNvbmZpZwplY2hvIEVDU19JTUFHRV9DTEVBTlVQX0lOVEVSVkFMPTEwbWlucyA+PiAvZXRjL2Vjcy9lY3MuY29uZmlnOwplY2hvIEVDU19FTkdJTkVfVEFTS19DTEVBTlVQX1dBSVRfRFVSQVRJT049MTBtaW5zID4+IC9ldGMvZWNzL2Vjcy5jb25maWc7CmVjaG8gRUNTX0lNQUdFX01JTklNVU1fQ0xFQU5VUF9BR0U9MTVtaW5zID4+IC9ldGMvZWNzL2Vjcy5jb25maWc7CmVjaG8gRUNTX0VOQUJMRV9TUE9UX0lOU1RBTkNFX0RSQUlOSU5HPXRydWUgPj4gL2V0Yy9lY3MvZWNzLmNvbmZpZzsKSU5TVEFOQ0VfSUQ9JChjYXQgL3Zhci9saWIvY2xvdWQvZGF0YS9pbnN0YW5jZS1pZCk7CmVjaG8gRUNTX0lOU1RBTkNFX0FUVFJJQlVURVM9e1wiZWMyX2luc3RhbmNlXCI6XCIkSU5TVEFOQ0VfSURcIn0gPj4gL2V0Yy9lY3MvZWNzLmNvbmZpZzsKCnl1bSBpbnN0YWxsICAteSBhd3MtY2xpCm1rZGlyIC1wIC9yb290L2V4dHJhX2V0YwoKQkFTRV9TM19CVUNLRVRfUEFUSD0nemFuYWx5dGljcycKQ0xVU1RFUl9UWVBFPSdyZXBvcnRpbmctY2x1c3RlcicKCmF3cyBzMyBjcCBzMzovLyR7QkFTRV9TM19CVUNLRVRfUEFUSH0vY29uZmlnL2Vtci90cmlub2RiLyAvcm9vdC9leHRyYV9ldGMvIC0tcmVjdXJzaXZlIC0tZXhjbHVkZSAnKicgIC0taW5jbHVkZSAnKnJ1bGVzLmpzb24nCmF3cyBzMyBjcCBzMzovLyR7QkFTRV9TM19CVUNLRVRfUEFUSH0vdHJpbm9kYi9ncm91cC50eHQgL3Jvb3QvZXh0cmFfZXRjL2dyb3VwLnR4dAovYmluL2Jhc2ggLWMgIihjcm9udGFiIC1sIDI+L2Rldi9udWxsOyBlY2hvICcqICogKiAqICogYXdzIHMzIGNwIHMzOi8vJHtCQVNFX1MzX0JVQ0tFVF9QQVRIfS9jb25maWcvZW1yL3RyaW5vZGIvIC9yb290L2V4dHJhX2V0Yy8gLS1yZWN1cnNpdmUgLS1leGNsdWRlICInIioiJyIgIC0taW5jbHVkZSAiJyIqcnVsZXMuanNvbiInIiA+PiAvcm9vdC9ydWxlcy5sb2cgIDI+JjEnKSB8IGNyb250YWIgLSIKL2Jpbi9iYXNoIC1jICIoY3JvbnRhYiAtbCAyPi9kZXYvbnVsbDsgZWNobyAnKiAqICogKiAqIGF3cyBzMyBjcCBzMzovLyR7QkFTRV9TM19CVUNLRVRfUEFUSH0vdHJpbm9kYi9ncm91cC50eHQgL3Jvb3QvZXh0cmFfZXRjL2dyb3VwLnR4dCA+PiAvcm9vdC9ncm91cC5sb2cgMj4mMScpIHwgY3JvbnRhYiAtIgoKYXdzIHMzIGNwIHMzOi8vemFuYWx5dGljcy9jb25maWcvZW1yL2NvbW1vbi9zY3JpcHRzL2Ftem4yL2Jvb3RzdHJhcC1lc3NlbnRpYWxzLXRpbWV6b25lLnNoCSAvcm9vdC9ib290c3RyYXAtZXNzZW50aWFscy10aW1lem9uZS5zaApiYXNoIC9yb290L2Jvb3RzdHJhcC1lc3NlbnRpYWxzLXRpbWV6b25lLnNo"
  vpc_security_group_ids = [
    "sg-085d25a15dd3fd2a0",
    "sg-0fa1113efaa345a7d",
  ]

  block_device_mappings {
    device_name = "/dev/xvda"

    ebs {
      delete_on_termination = "true"
      encrypted             = "true"
      iops                  = 3000
      snapshot_id           = "snap-017e2cc967b917280"
      throughput            = 125
      volume_size           = 256
      volume_type           = "gp3"
    }
  }

  iam_instance_profile {
    arn = data.aws_iam_instance_profile.ecs_instance_role.arn
  }

  monitoring {
    enabled = true
  }
}