resource "aws_autoscaling_group" "blinkit_etl_worker_4_asg" {
  capacity_rebalance = true
  default_cooldown   = 300
  desired_capacity   = 14
  enabled_metrics = [
    "GroupAndWarmPoolDesiredCapacity",
    "GroupAndWarmPoolTotalCapacity",
    "GroupDesiredCapacity",
    "GroupInServiceCapacity",
    "GroupInServiceInstances",
    "GroupMaxSize",
    "GroupMinSize",
    "GroupPendingCapacity",
    "GroupPendingInstances",
    "GroupStandbyCapacity",
    "GroupStandbyInstances",
    "GroupTerminatingCapacity",
    "GroupTerminatingInstances",
    "GroupTotalCapacity",
    "GroupTotalInstances",
    "WarmPoolDesiredCapacity",
    "WarmPoolMinSize",
    "WarmPoolPendingCapacity",
    "WarmPoolTerminatingCapacity",
    "WarmPoolTotalCapacity",
    "WarmPoolWarmedCapacity",
  ]
  health_check_grace_period = 60
  health_check_type         = "EC2"
  load_balancers            = []
  max_instance_lifetime     = 0
  max_size                  = 30
  metrics_granularity       = "1Minute"
  min_size                  = 0
  name                      = "blinkit-etl-trino-worker-4-arch-asg"
  protect_from_scale_in     = true
  service_linked_role_arn   = "arn:aws:iam::183295456051:role/aws-service-role/autoscaling.amazonaws.com/AWSServiceRoleForAutoScaling"
  suspended_processes       = []
  target_group_arns         = []
  termination_policies      = []
  vpc_zone_identifier       = [data.aws_subnet.trino_subnet.id]

  mixed_instances_policy {
    instances_distribution {
      on_demand_allocation_strategy            = "prioritized"
      on_demand_base_capacity                  = 0
      on_demand_percentage_above_base_capacity = 0
      spot_allocation_strategy                 = "price-capacity-optimized"
      spot_instance_pools                      = 0
    }

    launch_template {
      launch_template_specification {
        launch_template_id   = "lt-097a3499c11fbdeac"
        launch_template_name = "blinkit-etl-trino-amd-lt"
        version              = "$Default"
      }

      override {
        instance_type = "r7gd.4xlarge"
        launch_template_specification {
          launch_template_id = "lt-0095e2194b4d73e97"
        }
      }
      override {
        instance_type = "r7i.4xlarge"
      }
      override {
        instance_type = "r5a.4xlarge"
      }
      override {
        instance_type = "r5n.4xlarge"
      }
      override {
        instance_type = "r5b.4xlarge"
      }
      override {
        instance_type = "r5dn.4xlarge"
      }
      override {
        instance_type = "r6in.4xlarge"
      }
      override {
        instance_type = "r5ad.4xlarge"
      }
      override {
        instance_type = "r5d.4xlarge"
      }
    }
  }

  tag {
    key                 = "AmazonECSManaged"
    propagate_at_launch = true
    value               = ""
  }

  timeouts {}

  lifecycle {
    ignore_changes = [
      desired_capacity,
      max_size,
      min_size
    ]
  }
}

resource "aws_autoscaling_lifecycle_hook" "blinkit_etl_worker_4_lifecycle_hook" {
  name                   = "terminate-instance-hook"
  autoscaling_group_name = "blinkit-etl-trino-worker-4-arch-asg"
  default_result         = "CONTINUE" # or "ABANDON" to terminate immediately

  heartbeat_timeout    = 300 # Timeout in seconds before the instance is terminated
  lifecycle_transition = "autoscaling:EC2_INSTANCE_TERMINATING"
}
