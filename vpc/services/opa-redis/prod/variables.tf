variable "service_name" {
  type        = string
  description = "Name of the service"
  default     = "opa-redis"
}

variable "environment" {
  type        = string
  description = "Environment name"
  default     = "prod"
}

variable "vpc_id" {
  type        = string
  description = "VPC ID for analytics services"
}

variable "subnet_id_1a" {
  type        = string
  description = "Subnet ID for availability zone 1a"
}

variable "subnet_id_1b" {
  type        = string
  description = "Subnet ID for availability zone 1b"
}

variable "redis_cluster_config" {
  type = object({
    node_type = string
    shards    = number
    replicas  = number
  })
  description = "Redis cluster configuration"
  default = {
    node_type = "cache.t4g.medium"
    shards    = 1
    replicas  = 1
  }
} 