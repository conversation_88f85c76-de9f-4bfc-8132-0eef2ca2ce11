resource "aws_elasticache_subnet_group" "opa_redis_subnet_group" {
  name       = "${var.environment}-${var.service_name}-subnet-group"
  subnet_ids = [var.subnet_id_1a, var.subnet_id_1b]

  tags = {
    Name                     = "${var.environment}-${var.service_name} ElastiCache Subnet Group"
    "cost:application"       = var.service_name
    "cost:component"         = "elasticache-subnet-group"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  }
} 