resource "aws_security_group" "opa_redis_cache_sg" {
  name        = "${var.environment}-${var.service_name}-cache-sg"
  description = "Security group for ${var.environment}-${var.service_name} cache"
  vpc_id      = var.vpc_id

  ingress {
    description     = "Redis port from security group sg-0fbe6c5e8cf7e523f"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = ["sg-0fbe6c5e8cf7e523f"]
  }

  ingress {
    description     = "Redis port from security group sg-0ae2bf8dcd0b74ed2"
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = ["sg-0ae2bf8dcd0b74ed2"]
  }

  ingress {
    description = "Redis port from specific machine"
    from_port   = 6379
    to_port     = 6379
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name                     = "${var.environment}-${var.service_name}-cache-sg"
    "cost:application"       = var.service_name
    "cost:component"         = "security-group"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = "true"
  }
} 