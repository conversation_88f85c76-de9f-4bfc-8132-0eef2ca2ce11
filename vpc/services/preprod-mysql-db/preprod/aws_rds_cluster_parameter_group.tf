resource "aws_rds_cluster_parameter_group" "rds_cluster_parameter_group" {
  name        = "${var.environment}-${var.service_name}-rds-cluster-parameter-group"
  description = "cluster parameter group for ${var.environment}-${var.service_name}-rds-cluster"
  family      = var.aurora_cluster_config.engine_family

  tags = {
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
    "temp:testing"           = true
  }
} 