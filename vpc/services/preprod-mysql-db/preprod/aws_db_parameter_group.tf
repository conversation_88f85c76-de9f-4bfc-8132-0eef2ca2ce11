resource "aws_db_parameter_group" "db_parameter_group" {
  name        = "${var.environment}-${var.service_name}-db-parameter-group"
  description = "db parameter group for ${var.environment}-${var.service_name}-db"
  family      = var.aurora_cluster_config.engine_family

  tags = {
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
    "temp:testing"           = true
  }
} 