resource "aws_security_group" "cluster_sg" {
  name        = "${var.environment}-${var.service_name}-sg"
  description = "Security group for ${var.environment}-${var.service_name} cluster"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 3306  # MySQL port
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*********/16"]  # Internal network access
    description = "MySQL access from internal network"
  }
  ingress {
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
    description = "MySQL access from presto machine network"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
    description = "All outbound traffic"
  }

  tags = {
    Name                     = "${var.environment}-${var.service_name}-sg"
    "cost:application"       = var.service_name
    "cost:component"         = "security-group"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
    "temp:testing"           = true
    "karpenter.sh/discovery" = "eks-preprod-apse1-analytics"
  }
} 