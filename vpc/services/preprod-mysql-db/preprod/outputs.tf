output "rds_cluster_endpoint" {
  description = "RDS Aurora cluster endpoint"
  value       = aws_rds_cluster.aurora_mysql.endpoint
}

output "rds_cluster_reader_endpoint" {
  description = "RDS Aurora cluster reader endpoint"
  value       = aws_rds_cluster.aurora_mysql.reader_endpoint
}

output "rds_cluster_identifier" {
  description = "RDS Aurora cluster identifier"
  value       = aws_rds_cluster.aurora_mysql.cluster_identifier
}

output "database_name" {
  description = "Database name"
  value       = aws_rds_cluster.aurora_mysql.database_name
}

output "master_username" {
  description = "Master username"
  value       = aws_rds_cluster.aurora_mysql.master_username
  sensitive   = true
}

output "ssm_parameter_name" {
  description = "SSM parameter name for database password"
  value       = aws_ssm_parameter.db_password.name
}

output "security_group_id" {
  description = "Security group ID for the RDS cluster"
  value       = aws_security_group.cluster_sg.id
}

output "connection_info" {
  description = "MySQL connection information"
  value = {
    endpoint = aws_rds_cluster.aurora_mysql.endpoint
    port     = aws_rds_cluster.aurora_mysql.port
    database = aws_rds_cluster.aurora_mysql.database_name
    username = aws_rds_cluster.aurora_mysql.master_username
  }
  sensitive = true
} 