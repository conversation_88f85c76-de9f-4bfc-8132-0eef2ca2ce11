resource "aws_rds_cluster" "aurora_mysql" {
  cluster_identifier     = "${var.environment}-${var.service_name}-aurora"
  engine                 = var.aurora_cluster_config.engine
  engine_mode            = var.aurora_cluster_config.engine_mode
  engine_version         = var.aurora_cluster_config.engine_version
  database_name          = "prod_trino_gateway"
  master_username        = var.aurora_cluster_config.master_username
  master_password        = random_password.db_password.result
  availability_zones     = var.availability_zones
  vpc_security_group_ids = [data.aws_security_group.rds_security_group.id]
  db_subnet_group_name   = aws_db_subnet_group.trino_gateway_subnet_group.name

  copy_tags_to_snapshot = true
  storage_encrypted     = true
  skip_final_snapshot   = true
  deletion_protection   = false

  db_cluster_parameter_group_name = aws_rds_cluster_parameter_group.rds_cluster_parameter_group.name
  preferred_backup_window         = "23:30-01:30"
  preferred_maintenance_window    = "wed:22:30-wed:23:20"

  tags = {
    Name                     = "${var.environment}-${var.service_name}-aurora"
    "cost:application"       = var.service_name
    "cost:component"         = var.component
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
}

resource "aws_rds_cluster_instance" "aurora_mysql_instance" {
  identifier         = "${var.environment}-${var.service_name}-aurora-instance"
  cluster_identifier = aws_rds_cluster.aurora_mysql.cluster_identifier

  engine                  = aws_rds_cluster.aurora_mysql.engine
  engine_version          = aws_rds_cluster.aurora_mysql.engine_version
  instance_class          = var.instance_size
  db_parameter_group_name = aws_db_parameter_group.db_parameter_group.name

  tags = {
    "cost:application"       = var.service_name
    "cost:component"         = var.component
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
}

resource "random_password" "db_password" {
  length  = 16
  special = true
} 