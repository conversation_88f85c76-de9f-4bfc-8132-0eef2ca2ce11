resource "aws_db_subnet_group" "trino_gateway_subnet_group" {
  name       = "${var.environment}-${var.service_name}-subnet-group"
  subnet_ids = [data.aws_subnet.subnet_1a.id, data.aws_subnet.subnet_1b.id]

  tags = {
    Name                     = "${var.environment}-${var.service_name}-subnet-group"
    "cost:application"       = var.service_name
    "cost:component"         = "subnet-group"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
} 