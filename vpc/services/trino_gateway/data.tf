

data "aws_iam_instance_profile" "trino_gateway_instance_profile" {
  name = "prod-blinkit-trino-gateway-role"
}

data "aws_security_group" "rds_security_group" {
  id = "sg-0ecbf8afe80038220"
}

data "aws_security_group" "internal_ssh" {
  id = "sg-085d25a15dd3fd2a0"
}

data "aws_security_group" "prod_trino_ecs" {
  id = "sg-0fa1113efaa345a7d"
}

data "aws_ami" "trino_gateway" {
  most_recent = true
  owners      = ["self", "amazon"]

  filter {
    name   = "image-id"
    values = ["ami-016fc512f18751c3f"]
  }
}

data "aws_subnet" "subnet_1a" {
  id = "subnet-03ca306593d8ce024"
}

data "aws_subnet" "subnet_1b" {
  id = "subnet-091d72e204a90b03e"
}

data "aws_subnet" "subnet_1c" {
  id = "subnet-0b813d0d0e3356875"
}

data "aws_vpc" "prod_vpc" {
  id = "vpc-0e96120703d90e9e9"
} 