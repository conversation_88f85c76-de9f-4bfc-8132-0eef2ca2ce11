variable "service_name" {
  type = string
}

variable "environment" {
  type    = string
  default = "prod"
}

variable "aws_provider_region" {
  type = string
}

variable "availability_zones" {
  type = list(string)
}

variable "instance_size" {
  type = string
}

variable "component" {
  type = string
}

variable "aws_profile" {
  type = string
}

variable "aurora_cluster_config" {
  type = object({
    master_username = string
    engine          = string
    engine_mode     = string
    engine_version  = string
    engine_family   = string
  })
}

# EC2 Configuration Variables
variable "ec2_instance_type" {
  description = "EC2 instance type"
  type        = string
}

variable "key_name" {
  description = "Key pair name for EC2 access"
  type        = string
}

variable "mysql_port" {
  description = "MySQL port number"
  type        = number
  default     = 3306
} 