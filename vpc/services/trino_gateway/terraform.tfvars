environment           = "prod"
service_name          = "trino-gateway"
aws_provider_region   = "ap-southeast-1"
availability_zones    = ["ap-southeast-1a", "ap-southeast-1b"]
instance_size         = "db.r5.large"  
component             = "aurora"
aws_profile           = "blinkit_analytics"

aurora_cluster_config = {
  master_username = "admin"
  engine          = "aurora-mysql"
  engine_mode     = "provisioned"
  engine_version  = "8.0.mysql_aurora.3.04.0"
  engine_family   = "aurora-mysql8.0"
}

# EC2 Configuration
ec2_instance_type      = "c5.xlarge"
key_name              = "prod-trino"

mysql_port           = 3306 