resource "aws_instance" "trino_gateway" {
  ami                    = data.aws_ami.trino_gateway.id
  instance_type          = var.ec2_instance_type
  subnet_id              = data.aws_subnet.subnet_1b.id
  vpc_security_group_ids = [data.aws_security_group.internal_ssh.id, data.aws_security_group.prod_trino_ecs.id]
  key_name               = var.key_name
  iam_instance_profile   = data.aws_iam_instance_profile.trino_gateway_instance_profile.name
  ebs_optimized         = true
  
  cpu_options {
    core_count       = 2
    threads_per_core = 2
  }
  
  metadata_options {
    http_tokens                 = "required"
    http_put_response_hop_limit = 2
    http_endpoint              = "enabled"
  }
  
  tags = {
    Name                     = "${var.environment}-${var.service_name}"
    "cost:application"       = var.service_name
    "cost:component"         = "ec2"
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
} 