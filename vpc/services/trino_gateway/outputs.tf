output "rds_cluster_endpoint" {
  description = "RDS Aurora cluster endpoint"
  value       = aws_rds_cluster.aurora_mysql.endpoint
}

output "rds_cluster_reader_endpoint" {
  description = "RDS Aurora cluster reader endpoint"
  value       = aws_rds_cluster.aurora_mysql.reader_endpoint
}

output "rds_cluster_identifier" {
  description = "RDS Aurora cluster identifier"
  value       = aws_rds_cluster.aurora_mysql.cluster_identifier
}

output "database_name" {
  description = "Database name"
  value       = aws_rds_cluster.aurora_mysql.database_name
}

output "master_username" {
  description = "Master username"
  value       = aws_rds_cluster.aurora_mysql.master_username
  sensitive   = true
}

output "ssm_parameter_name" {
  description = "SSM parameter name for database password"
  value       = aws_ssm_parameter.db_password.name
}

output "security_group_id" {
  description = "Security group ID for the RDS cluster"
  value       = data.aws_security_group.rds_security_group.id
}

output "connection_info" {
  description = "MySQL connection information"
  value = {
    endpoint = aws_rds_cluster.aurora_mysql.endpoint
    port     = aws_rds_cluster.aurora_mysql.port
    database = aws_rds_cluster.aurora_mysql.database_name
    username = aws_rds_cluster.aurora_mysql.master_username
  }
  sensitive = true
}

output "ec2_instance_id" {
  description = "EC2 instance ID"
  value       = aws_instance.trino_gateway.id
}

output "ec2_private_ip" {
  description = "EC2 private IP"
  value       = aws_instance.trino_gateway.private_ip
}

# Data Source Outputs
output "vpc_id" {
  description = "VPC ID"
  value       = data.aws_vpc.prod_vpc.id
}

output "vpc_cidr" {
  description = "VPC CIDR block"
  value       = data.aws_vpc.prod_vpc.cidr_block
}

output "subnet_1a_id" {
  description = "Subnet 1a ID"
  value       = data.aws_subnet.subnet_1a.id
}

output "subnet_1b_id" {
  description = "Subnet 1b ID"
  value       = data.aws_subnet.subnet_1b.id
}

output "subnet_1c_id" {
  description = "Subnet 1c ID"
  value       = data.aws_subnet.subnet_1c.id
}

output "ami_id" {
  description = "AMI ID used for EC2 instance"
  value       = data.aws_ami.trino_gateway.id
}

output "internal_ssh_sg_id" {
  description = "Internal SSH Security Group ID"
  value       = data.aws_security_group.internal_ssh.id
}

output "prod_trino_ecs_sg_id" {
  description = "Prod Trino ECS Security Group ID"
  value       = data.aws_security_group.prod_trino_ecs.id
} 