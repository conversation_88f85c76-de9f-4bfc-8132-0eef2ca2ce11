environment           = "preprod"
service_name          = "cdp"
aws_provider_region   = "ap-southeast-1"
vpc_id                = "vpc-0e96120703d90e9e9"
availability_zones    = ["ap-southeast-1a", "ap-southeast-1b"]
instance_size         = "db.t4g.medium"  # Changed to smallest supported instance type for Aurora
component             = "aurora"
subnet_id_1b          = "subnet-06b312b17ae4764a3"
subnet_id_1a          = "subnet-09a51196787a38025"
aws_profile           = "blinkit_analytics"


aurora_cluster_config = {
  master_username = "root" 
  engine          = "aurora-postgresql"
  engine_mode     = "provisioned"
  engine_version  = "15.5"
  engine_family   = "aurora-postgresql15"
}
