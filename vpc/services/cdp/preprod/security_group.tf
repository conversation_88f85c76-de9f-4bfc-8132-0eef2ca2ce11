resource "aws_security_group" "cluster_sg" {
  name        = "${var.environment}-${var.service_name}-security-group"
  description = "security group for ${var.environment}-${var.service_name}"
  vpc_id      = var.vpc_id

  ingress {
    description = "Allow PostgreSQL inbound traffic from VPC"
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["*********/16"]
  }

  ingress {
    description = "Allow PostgreSQL inbound traffic from specific IP"
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    cidr_blocks = ["*************/32"]
  }

  ingress {
    description = "Allow HTTPS inbound traffic from VPN"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["**********/16"]
  }

  ingress {
    description = "Allow inbound traffic from within security group on port 3000"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    security_groups = ["sg-0e198197bab4e23f1"]
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name                    = "${var.environment}-${var.service_name}-security-group"
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
    "karpenter.sh/discovery" = "eks-prod-apse1-analytics"
  }
}
