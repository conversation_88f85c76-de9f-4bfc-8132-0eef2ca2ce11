variable "service_name" {
  type = string
}

variable "environment" {
  type    = string
  default = "prod"
}

variable "aws_provider_region" {
  type = string
}


variable "vpc_id" {
  type = string
}

variable "availability_zones" {
  type = list(string)
}

variable "instance_size" {
  type = string
}

variable "temporal_instance_size" {
  type = string
}

variable "component" {
  type = string
}

variable "subnet_id_1b" {
  type = string
}

variable "subnet_id_1a" {
  type = string
}

variable "aws_profile" {
  type = string
}

variable "aurora_cluster_config" {
  type = object({
    master_username = string
    engine          = string
    engine_mode     = string
    engine_version  = string
    engine_family   = string
  })
}

variable "temporal_aurora_cluster_config" {
  type = object({
    master_username = string
    engine          = string
    engine_mode     = string
    engine_version  = string
    engine_family   = string
  })
}
