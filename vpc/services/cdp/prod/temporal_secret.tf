resource "random_password" "temporal_db_password" {
  length           = 12
  special          = true
  override_special = "_%!^"
}

resource "aws_ssm_parameter" "temporal_db_password_param" {
  name        = "/data-platform/cdp/db/cdp-prod-temporal/root-password"
  description = "Database password for cdp-prod-temporal"
  type        = "SecureString"
  value       = random_password.temporal_db_password.result
}