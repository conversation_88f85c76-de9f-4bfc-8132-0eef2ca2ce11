resource "aws_db_parameter_group" "temporal_db_parameter_group" {
  name        = "${var.environment}-${var.service_name}-temporal-db-parameter-group"
  description = "db parameter group for ${var.environment}-${var.service_name}-temporal-db"
  family      = var.temporal_aurora_cluster_config.engine_family

  tags = {
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
}
