resource "aws_rds_cluster" "temporal_aurora_postgres" {
  cluster_identifier     = "${var.environment}-${var.service_name}-temporal-aurora"
  engine                 = var.temporal_aurora_cluster_config.engine
  engine_mode           = var.temporal_aurora_cluster_config.engine_mode
  engine_version        = var.temporal_aurora_cluster_config.engine_version
  database_name         = "temporal"
  master_username       = var.temporal_aurora_cluster_config.master_username
  master_password       = random_password.temporal_db_password.result
  availability_zones    = var.availability_zones
  vpc_security_group_ids = [aws_security_group.cluster_sg.id]
  db_subnet_group_name  = aws_db_subnet_group.prod_cdp_subnet_group.name

  copy_tags_to_snapshot = true
  storage_encrypted     = true
  skip_final_snapshot   = true
  deletion_protection   = false

  db_cluster_parameter_group_name = aws_rds_cluster_parameter_group.temporal_rds_cluster_parameter_group.name
  preferred_backup_window         = "23:30-01:30"
  preferred_maintenance_window    = "wed:22:30-wed:23:20"

  tags = {
    Name                    = "${var.environment}-${var.service_name}-temporal-aurora"
    "cost:application"      = var.service_name
    "cost:component"        = var.component
    "cost:environment"      = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
}

resource "aws_rds_cluster_instance" "temporal_aurora_postgres_instance" {
  identifier         = "${var.environment}-${var.service_name}-temporal-aurora-instance"
  cluster_identifier = aws_rds_cluster.temporal_aurora_postgres.cluster_identifier
  
  engine            = aws_rds_cluster.temporal_aurora_postgres.engine
  engine_version    = aws_rds_cluster.temporal_aurora_postgres.engine_version
  instance_class    = var.temporal_instance_size
  db_parameter_group_name = aws_db_parameter_group.temporal_db_parameter_group.name

  tags = {
    "cost:application"       = var.service_name
    "cost:component"         = var.component
    "cost:environment"       = var.environment
    "blinkit:resource_group" = var.service_name
    "blinkit:terraform"      = true
  }
}