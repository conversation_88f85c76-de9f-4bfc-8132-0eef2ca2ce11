variable "cluster_name" {
  description = "Cluster name."
}

variable "trusted_cidr_blocks" {
  description = "List of trusted subnets CIDRs with hosts that should connect to the cluster. E.g., subnets with ALB and bastion hosts."
  type        = list(string)
  default     = [""]
}

variable "instance_types" {
  description = "ECS node instance types. Maps of pairs like `type = weight`. Where weight gives the instance type a proportional weight to other instance types."
  type        = map(any)
  default = {
    "t3a.small" = 2
  }
}

variable "cp_tags" {
  type = map(any)
}

variable "protect_from_scale_in" {
  description = "The autoscaling group will not select instances with this setting for termination during scale in events."
  default     = true
}

variable "asg_min_size" {
  description = "The minimum size the auto scaling group (measured in EC2 instances)."
  default     = 0
}

variable "asg_max_size" {
  description = "The maximum size the auto scaling group (measured in EC2 instances)."
  default     = 10
}

variable "spot" {
  description = "Choose should we use spot instances or on-demand to populate ECS cluster."
  type        = bool
  default     = false
}

variable "subnets_ids" {
  description = "IDs of subnets. Use subnets from various availability zones to make the cluster more reliable."
  type        = list(string)
}

variable "target_capacity" {
  description = "The target utilization for the cluster. A number between 1 and 100."
  default     = "100"
}

variable "on_demand_base_capacity" {
  description = "The minimum number of on-demand EC2 instances."
  default     = 0
}

variable "lt_id" {
  type = string
}

variable "asg_name" {
  type = string
}

variable "asg_key1" {
  type    = string
  default = "blinkit:terraform"
}

variable "asg_value1" {
  type    = string
  default = "true"
}

variable "cost_component" {
  type = string
}

variable "cost_application" {
  type = string
}

variable "od_distribution" {
  description = "The % of od nodes in instance distribution"
  default = 0
}