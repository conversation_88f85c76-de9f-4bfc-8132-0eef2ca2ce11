resource "aws_ecs_capacity_provider" "capacity_provider" {
  name = "${aws_autoscaling_group.templated_asg.name}-cp"

  auto_scaling_group_provider {
    auto_scaling_group_arn         = aws_autoscaling_group.templated_asg.arn
    managed_termination_protection = local.protect_from_scale_in ? "ENABLED" : "DISABLED"

    managed_scaling {
      status          = "ENABLED"
      target_capacity = local.target_capacity
    }
  }
  tags = local.tags
}

output "cp_name" {
  value = aws_ecs_capacity_provider.capacity_provider.name

}