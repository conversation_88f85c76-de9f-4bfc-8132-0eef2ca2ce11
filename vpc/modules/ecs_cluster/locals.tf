locals {
  asg_max_size            = var.asg_max_size
  asg_min_size            = var.asg_min_size
  instance_types          = var.instance_types
  name                    = replace(var.cluster_name, " ", "_")
  on_demand_base_capacity = var.on_demand_base_capacity
  protect_from_scale_in   = var.protect_from_scale_in
  spot                    = var.spot == true ? var.od_distribution : 100
  subnets_ids             = var.subnets_ids
  target_capacity         = var.target_capacity
  trusted_cidr_blocks     = var.trusted_cidr_blocks
  vpc_id                  = data.aws_subnet.default.vpc_id
  lt_id                   = var.lt_id
  asg_name                = var.asg_name
  asg_key1                = var.asg_key1
  asg_value1              = var.asg_value1
  cost_component          = var.cost_component
  cost_application        = var.cost_application
  tags                    = var.cp_tags
}