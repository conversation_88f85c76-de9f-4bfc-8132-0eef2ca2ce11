resource "aws_autoscaling_group" "templated_asg" {
  name                  = "${local.asg_name}-asg"
  max_size              = local.asg_max_size
  min_size              = local.asg_min_size
  vpc_zone_identifier   = local.subnets_ids
  protect_from_scale_in = local.protect_from_scale_in
  termination_policies  = ["Default"]
  default_cooldown      = 60
  capacity_rebalance    = true
  desired_capacity_type = "units"

  health_check_type         = "EC2"
  health_check_grace_period = 60

  enabled_metrics = [
    "GroupMinSize",
    "GroupMaxSize",
    "GroupDesiredCapacity",
    "GroupInServiceInstances",
    "GroupPendingInstances",
    "GroupStandbyInstances",
    "GroupTerminatingInstances",
    "GroupTotalInstances",
    "GroupInServiceCapacity",
    "GroupPendingCapacity",
    "GroupStandbyCapacity",
    "GroupTerminatingCapacity",
    "GroupTotalCapacity",
    "WarmPoolDesiredCapacity",
    "WarmPoolWarmedCapacity",
    "WarmPoolPendingCapacity",
    "WarmPoolTerminatingCapacity",
    "WarmPoolTotalCapacity",
    "GroupAndWarmPoolDesiredCapacity",
    "GroupAndWarmPoolTotalCapacity"
  ]

  mixed_instances_policy {
    instances_distribution {
      spot_allocation_strategy                 = "price-capacity-optimized"
      on_demand_base_capacity                  = local.on_demand_base_capacity
      on_demand_percentage_above_base_capacity = local.spot
    }

    launch_template {
      launch_template_specification {
        launch_template_id = local.lt_id
        version            = "$Latest"
      }

      dynamic "override" {
        for_each = local.instance_types

        content {
          instance_type     = override.key
          weighted_capacity = override.value
        }
      }
    }
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [desired_capacity, max_size, min_size]
  }

  tag {
    key                 = "AmazonECSManaged"
    propagate_at_launch = true
    value               = ""
  }

  tag {
    key                 = "Name"
    propagate_at_launch = true
    value               = local.name
  }

  tag {
    key                 = local.asg_key1
    propagate_at_launch = true
    value               = local.asg_value1
  }

  tag {
    key                 = "cost:component"
    propagate_at_launch = true
    value               = local.cost_component
  }

  tag {
    key                 = "cost:application"
    propagate_at_launch = true
    value               = local.cost_application
  }
}

output "asg_name" {
  value = aws_autoscaling_group.templated_asg.name

}